import databaseService from '../services/DatabaseService';

// 临时清理脚本 - 清理重复的默认账本
export const cleanupDuplicateAccountBooks = async () => {
  try {
    console.log('开始清理重复的默认账本...');
    await databaseService.cleanupDuplicateDefaultAccountBooks();
    console.log('清理完成！');
    return { success: true, message: '重复的默认账本已清理完成' };
  } catch (error) {
    console.error('清理失败:', error);
    return { success: false, message: '清理失败: ' + error.message };
  }
};

// 查看当前账本状态
export const checkAccountBooksStatus = async () => {
  try {
    const accountBooks = await databaseService.getAllAccountBooks();
    const defaultBooks = accountBooks.filter(book => book.isDefault);
    
    console.log('=== 账本状态检查 ===');
    console.log(`总账本数量: ${accountBooks.length}`);
    console.log(`默认账本数量: ${defaultBooks.length}`);
    
    if (defaultBooks.length > 1) {
      console.log('⚠️ 发现多个默认账本:');
      defaultBooks.forEach((book, index) => {
        console.log(`  ${index + 1}. ID: ${book.id}, 名称: ${book.name}, 创建时间: ${book.createdAt}`);
      });
    } else if (defaultBooks.length === 1) {
      console.log('✅ 默认账本正常:', defaultBooks[0]);
    } else {
      console.log('❌ 没有找到默认账本');
    }
    
    console.log('所有账本:');
    accountBooks.forEach((book, index) => {
      console.log(`  ${index + 1}. ${book.name} (ID: ${book.id}, 默认: ${book.isDefault ? '是' : '否'})`);
    });
    
    return {
      totalBooks: accountBooks.length,
      defaultBooks: defaultBooks.length,
      books: accountBooks
    };
  } catch (error) {
    console.error('检查账本状态失败:', error);
    return { error: error.message };
  }
};
