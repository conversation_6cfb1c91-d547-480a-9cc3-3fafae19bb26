import React, {useState, useEffect} from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  FlatList,
  Modal,
  TextInput,
  Alert,
  ActivityIndicator,
} from 'react-native';
import Icon from 'react-native-vector-icons/FontAwesome6';
import PageContainer from '../components/PageContainer';
import databaseService, {AccountBook} from '../services/DatabaseService';
import {useToast} from '../context/ToastContext';
import {useFocusEffect} from '@react-navigation/native';
import {COLORS} from '../utils/color';
import {useTheme} from '../context/ThemeContext';
import ConfirmDialog from '../components/ConfirmDialog';

const MyAccountBooksScreen = ({navigation}) => {
  const {colors} = useTheme();
  const {showToast} = useToast();

  const [accountBooks, setAccountBooks] = useState<AccountBook[]>([]);
  const [loading, setLoading] = useState(false);
  const [modalVisible, setModalVisible] = useState(false);
  const [editingBook, setEditingBook] = useState<AccountBook | null>(null);
  const [bookName, setBookName] = useState('');
  const [bookDescription, setBookDescription] = useState('');
  const [confirmDialogVisible, setConfirmDialogVisible] = useState(false);
  const [bookToDelete, setBookToDelete] = useState<AccountBook | null>(null);

  // 加载账本列表
  const loadAccountBooks = async () => {
    try {
      setLoading(true);
      const books = await databaseService.getAllAccountBooks();
      setAccountBooks(books);
    } catch (error) {
      console.error('加载账本列表失败:', error);
      showToast('加载账本列表失败', 'error');
    } finally {
      setLoading(false);
    }
  };

  // 页面获得焦点时重新加载数据
  useFocusEffect(
    React.useCallback(() => {
      loadAccountBooks();
    }, []),
  );

  // 打开新增/编辑弹窗
  const openModal = (book?: AccountBook) => {
    if (book) {
      setEditingBook(book);
      setBookName(book.name);
      setBookDescription(book.description || '');
    } else {
      setEditingBook(null);
      setBookName('');
      setBookDescription('');
    }
    setModalVisible(true);
  };

  // 关闭弹窗
  const closeModal = () => {
    setModalVisible(false);
    setEditingBook(null);
    setBookName('');
    setBookDescription('');
  };

  // 保存账本
  const saveAccountBook = async () => {
    if (!bookName.trim()) {
      showToast('请输入账本名称', 'warning');
      return;
    }

    try {
      if (editingBook) {
        // 编辑账本
        await databaseService.updateAccountBook({
          ...editingBook,
          name: bookName.trim(),
          description: bookDescription.trim(),
        });
        showToast('账本更新成功', 'success');
      } else {
        // 新增账本
        await databaseService.addAccountBook({
          name: bookName.trim(),
          description: bookDescription.trim(),
          isDefault: false,
        });
        showToast('账本创建成功', 'success');
      }
      
      closeModal();
      loadAccountBooks();
    } catch (error) {
      console.error('保存账本失败:', error);
      showToast('保存账本失败', 'error');
    }
  };

  // 删除账本
  const deleteAccountBook = async () => {
    if (!bookToDelete) return;

    try {
      await databaseService.deleteAccountBook(bookToDelete.id!);
      showToast('账本删除成功', 'success');
      setConfirmDialogVisible(false);
      setBookToDelete(null);
      loadAccountBooks();
    } catch (error) {
      console.error('删除账本失败:', error);
      showToast(error.message || '删除账本失败', 'error');
      setConfirmDialogVisible(false);
      setBookToDelete(null);
    }
  };

  // 确认删除
  const confirmDelete = (book: AccountBook) => {
    setBookToDelete(book);
    setConfirmDialogVisible(true);
  };

  // 查看账本账单
  const viewAccountBookBills = (book: AccountBook) => {
    navigation.navigate('bills', {
      accountBookId: book.id,
      accountBookName: book.name,
    });
  };

  // 渲染账本项
  const renderAccountBookItem = ({item}: {item: AccountBook}) => (
    <View style={styles.bookItem}>
      <View style={styles.bookHeader}>
        <View style={styles.bookInfo}>
          <View style={styles.bookTitleRow}>
            <Text style={styles.bookName}>{item.name}</Text>
            {item.isDefault && (
              <View style={styles.defaultBadge}>
                <Text style={styles.defaultBadgeText}>默认</Text>
              </View>
            )}
          </View>
          {item.description && (
            <Text style={styles.bookDescription}>{item.description}</Text>
          )}
        </View>
        
        <View style={styles.bookActions}>
          <TouchableOpacity
            style={[styles.actionButton, styles.viewButton]}
            onPress={() => viewAccountBookBills(item)}
            activeOpacity={0.7}>
            <Icon name="eye" size={16} color="#007AFF" />
            <Text style={styles.viewButtonText}>查看</Text>
          </TouchableOpacity>
          
          <TouchableOpacity
            style={[styles.actionButton, styles.editButton]}
            onPress={() => openModal(item)}
            activeOpacity={0.7}>
            <Icon name="pen" size={14} color="#FF9500" />
          </TouchableOpacity>
          
          {!item.isDefault && (
            <TouchableOpacity
              style={[styles.actionButton, styles.deleteButton]}
              onPress={() => confirmDelete(item)}
              activeOpacity={0.7}>
              <Icon name="trash" size={14} color="#FF3B30" />
            </TouchableOpacity>
          )}
        </View>
      </View>
    </View>
  );

  // 渲染空状态
  const renderEmptyState = () => (
    <View style={styles.emptyContainer}>
      <Icon name="book" size={48} color={COLORS.text.gray} />
      <Text style={styles.emptyText}>还没有账本</Text>
      <Text style={styles.emptySubText}>点击右上角 + 号创建第一个账本</Text>
    </View>
  );

  return (
    <PageContainer
      headerTitle="我的账本"
      backgroundColor={COLORS.background.light}
      rightComponent={
        <TouchableOpacity
          style={styles.addButton}
          onPress={() => openModal()}
          activeOpacity={0.7}>
          <Icon name="plus" size={20} color={colors.primary} />
        </TouchableOpacity>
      }>
      
      {loading ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={colors.primary} />
        </View>
      ) : (
        <FlatList
          data={accountBooks}
          renderItem={renderAccountBookItem}
          keyExtractor={item => item.id?.toString() || ''}
          contentContainerStyle={styles.listContainer}
          ListEmptyComponent={renderEmptyState}
          showsVerticalScrollIndicator={false}
        />
      )}

      {/* 新增/编辑弹窗 */}
      <Modal
        visible={modalVisible}
        transparent={true}
        animationType="fade"
        onRequestClose={closeModal}>
        <View style={styles.modalOverlay}>
          <View style={styles.modalContent}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>
                {editingBook ? '编辑账本' : '新增账本'}
              </Text>
              <TouchableOpacity onPress={closeModal}>
                <Icon name="xmark" size={18} color={COLORS.text.gray} />
              </TouchableOpacity>
            </View>

            <View style={styles.inputContainer}>
              <Text style={styles.inputLabel}>账本名称</Text>
              <TextInput
                style={styles.textInput}
                value={bookName}
                onChangeText={setBookName}
                placeholder="请输入账本名称"
                placeholderTextColor={COLORS.text.placeholder}
                maxLength={20}
              />
            </View>

            <View style={styles.inputContainer}>
              <Text style={styles.inputLabel}>账本描述（可选）</Text>
              <TextInput
                style={[styles.textInput, styles.textArea]}
                value={bookDescription}
                onChangeText={setBookDescription}
                placeholder="请输入账本描述"
                placeholderTextColor={COLORS.text.placeholder}
                multiline={true}
                numberOfLines={3}
                maxLength={100}
              />
            </View>

            <View style={styles.modalButtons}>
              <TouchableOpacity
                style={[styles.modalButton, styles.cancelButton]}
                onPress={closeModal}>
                <Text style={styles.cancelButtonText}>取消</Text>
              </TouchableOpacity>
              
              <TouchableOpacity
                style={[styles.modalButton, styles.saveButton, {backgroundColor: colors.primary}]}
                onPress={saveAccountBook}>
                <Text style={styles.saveButtonText}>保存</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>

      {/* 删除确认对话框 */}
      <ConfirmDialog
        visible={confirmDialogVisible}
        title="删除账本"
        message={`确定要删除账本"${bookToDelete?.name}"吗？删除后无法恢复。`}
        onCancel={() => {
          setConfirmDialogVisible(false);
          setBookToDelete(null);
        }}
        onConfirm={deleteAccountBook}
        cancelText="取消"
        confirmText="删除"
      />
    </PageContainer>
  );
};

const styles = StyleSheet.create({
  listContainer: {
    padding: 16,
    flexGrow: 1,
  },
  bookItem: {
    backgroundColor: COLORS.background.white,
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  bookHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
  },
  bookInfo: {
    flex: 1,
    marginRight: 12,
  },
  bookTitleRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 4,
  },
  bookName: {
    fontSize: 16,
    fontWeight: '600',
    color: COLORS.text.primary,
    marginRight: 8,
  },
  defaultBadge: {
    backgroundColor: COLORS.primary,
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 4,
  },
  defaultBadgeText: {
    fontSize: 10,
    color: '#FFFFFF',
    fontWeight: '500',
  },
  bookDescription: {
    fontSize: 14,
    color: COLORS.text.gray,
    lineHeight: 20,
  },
  bookActions: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  actionButton: {
    padding: 8,
    borderRadius: 6,
    marginLeft: 4,
  },
  viewButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 122, 255, 0.1)',
    paddingHorizontal: 12,
  },
  viewButtonText: {
    fontSize: 12,
    color: '#007AFF',
    marginLeft: 4,
    fontWeight: '500',
  },
  editButton: {
    backgroundColor: 'rgba(255, 149, 0, 0.1)',
  },
  deleteButton: {
    backgroundColor: 'rgba(255, 59, 48, 0.1)',
  },
  addButton: {
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: 'rgba(0, 122, 255, 0.1)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingTop: 100,
  },
  emptyText: {
    fontSize: 18,
    fontWeight: '600',
    color: COLORS.text.primary,
    marginTop: 16,
    marginBottom: 8,
  },
  emptySubText: {
    fontSize: 14,
    color: COLORS.text.gray,
    textAlign: 'center',
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContent: {
    width: '90%',
    backgroundColor: COLORS.background.white,
    borderRadius: 16,
    padding: 20,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 20,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: COLORS.text.primary,
  },
  inputContainer: {
    marginBottom: 16,
  },
  inputLabel: {
    fontSize: 14,
    fontWeight: '500',
    color: COLORS.text.primary,
    marginBottom: 8,
  },
  textInput: {
    borderWidth: 1,
    borderColor: COLORS.border.light,
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    color: COLORS.text.primary,
    backgroundColor: COLORS.background.light,
  },
  textArea: {
    height: 80,
    textAlignVertical: 'top',
  },
  modalButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 20,
  },
  modalButton: {
    flex: 1,
    height: 44,
    borderRadius: 8,
    justifyContent: 'center',
    alignItems: 'center',
  },
  cancelButton: {
    backgroundColor: COLORS.background.light,
    marginRight: 8,
  },
  cancelButtonText: {
    fontSize: 16,
    color: COLORS.text.gray,
    fontWeight: '500',
  },
  saveButton: {
    marginLeft: 8,
  },
  saveButtonText: {
    fontSize: 16,
    color: '#FFFFFF',
    fontWeight: '600',
  },
});

export default MyAccountBooksScreen;
