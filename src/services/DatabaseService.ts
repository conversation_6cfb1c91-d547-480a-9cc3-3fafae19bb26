import SQLite from 'react-native-sqlite-storage';
import dayjs from 'dayjs';
import {COLORS} from '../utils/color';

// 启用 Promise 包装器
SQLite.enablePromise(true);

export interface Transaction {
  id?: string;
  amount: number;
  note: string;
  date: string;
  categoryId: string;
  categoryName: string;
  type: 'expense' | 'income';
  familyId?: number | null;
  familyName?: string;
  isReimbursable: boolean;
  accountBookId?: number;
  accountBookName?: string;
}

export interface Category {
  id?: string;
  name: string;
  isExpense: boolean;
  icon: string;
}

export interface BudgetSettings {
  daily: string;
  monthly: string;
  yearly: string;
  enabled: boolean;
}

export interface AutoExportSettings {
  enabled: boolean;
  lastExportDate?: string;
}

// 定义信用卡数据结构
export interface CreditCard {
  id?: string;
  bankName: string;
  lastThreeDigits: string;
  billingDay: number; // 出账日
  paymentDueDay: number; // 最后还款日
  color?: string; // 卡片颜色
  createdAt?: string;
  updatedAt?: string;
}

// 家庭相关类型
export interface Family {
  id: number;
  name: string;
  code: string;
  createdAt: string;
}

export interface FamilyMember {
  id: number;
  familyId: number;
  name: string;
  deviceId: string;
  avatar?: string;
  role: 'admin' | 'member';
  lastSyncTime?: string;
  isOnline?: boolean;
}

// 商品表结构
interface Product {
  id?: number;
  name: string;
  thumbnail?: string; // 存储图片的本地路径
  notes?: string;
  createdAt: string;
  updatedAt: string;
  purchased: number; // 0表示未购买，1表示已购买
}

// 商品价格表结构
interface ProductPrice {
  id?: number;
  productId: number;
  platform: string;
  price: number;
  isPurchasePrice?: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface LoanRepayment {
  amount: number;
  date: string;
}

export interface LoanRecord {
  id?: number;
  type: 'lend' | 'borrow';
  amount: number;
  note?: string;
  loanDate: string;
  repayments: LoanRepayment[];
}

// 账本接口
export interface AccountBook {
  id?: number;
  name: string;
  description?: string;
  isDefault: boolean;
  createdAt: string;
  updatedAt?: string;
}

class DatabaseService {
  private database: SQLite.SQLiteDatabase | null = null;
  private initialized: boolean = false;

  // 初始化数据库
  async initDatabase(): Promise<void> {
    // if (this.database) {
    //   console.log('数据库已初始化，重用现有连接');
    //   return;
    // }

    try {
      console.log('初始化数据库...');
      this.database = await SQLite.openDatabase({
        name: 'money_tracker.db',
        location: 'default',
      });
      console.log('数据库初始化成功');

      // 创建表
      await this.createTables();
      console.log('表创建成功');

      // 初始化默认分类
      await this.initDefaultCategories();
      console.log('默认分类初始化成功');

      // 初始化默认账本
      await this.initDefaultAccountBook();
      console.log('默认账本初始化成功');

      // 创建信用卡表
      await this.database!.executeSql(`
        CREATE TABLE IF NOT EXISTS credit_cards (
          id TEXT PRIMARY KEY,
          bank_name TEXT NOT NULL,
          last_three_digits TEXT NOT NULL,
          billing_day INTEGER NOT NULL,
          payment_due_day INTEGER NOT NULL,
          color TEXT,
          created_at TEXT,
          updated_at TEXT
        )
      `);

      console.log('信用卡表创建成功');

      // 创建分期表
      await this.createInstallmentTables();
      // 创建家庭表
      await this.createFamilyTables();

      // 创建商品相关表
      await this.createProductTables();

      // 其他表创建和初始化代码后添加这一行
      await this.updateDatabaseSchema();
      await this.ensureTransactionsTableHasFamilyColumn();

      // 确保表结构中包含所有必要的列
      await this.ensureProductTablesHaveRequiredColumns();

      // 创建借款相关表
      await this.createLoanTable(this.database);
      console.log('借款相关表创建成功');

      console.log('数据库初始化完成');
    } catch (error) {
      console.error('初始化数据库失败', error);
      throw error;
    }
  }

  // 添加一个方法，用于重新初始化数据库连接
  async reinitDatabase() {
    // 关闭现有连接
    if (this.database) {
      try {
        await this.database.close();
        console.log('关闭现有数据库连接');
      } catch (error) {
        console.error('关闭数据库连接失败', error);
      }
      this.database = null;
    }

    // 重新初始化
    await this.initDatabase();
  }

  // 初始化分类数据
  private async initializeCategories(): Promise<void> {
    try {
      // 支出分类
      const expenseCategories = [
        {name: '餐饮', isExpense: true, icon: 'utensils'},
        {name: '购物', isExpense: true, icon: 'bag-shopping'},
        {name: '交通', isExpense: true, icon: 'truck'},
        {name: '住房', isExpense: true, icon: 'house'},
        {name: '娱乐', isExpense: true, icon: 'film'},
        {name: '医疗', isExpense: true, icon: 'hospital'},
        {name: '教育', isExpense: true, icon: 'book'},
        {name: '旅行', isExpense: true, icon: 'plane'},
        {name: '通讯', isExpense: true, icon: 'mobile-screen'},
        {name: '服装', isExpense: true, icon: 'shirt'},
        {name: '美容', isExpense: true, icon: 'spa'},
        {name: '其他', isExpense: true, icon: 'snowflake'},
      ];

      // 收入分类
      const incomeCategories = [
        {name: '工资', isExpense: false, icon: 'money-bill'},
        {name: '奖金', isExpense: false, icon: 'award'},
        {name: '理财', isExpense: false, icon: 'chart-line'},
        {name: '兼职', isExpense: false, icon: 'briefcase'},
        {name: '礼金', isExpense: false, icon: 'gift'},
        {name: '退款', isExpense: false, icon: 'rotate-left'},
        {name: '其他', isExpense: false, icon: 'snowflake'},
      ];

      // 先检查是否已存在分类数据
      const [checkResult] = await this.database!.executeSql(
        'SELECT COUNT(*) as count FROM categories',
      );
      const count = checkResult.rows.item(0).count;

      if (count > 0) {
        console.log('分类数据已存在，跳过初始化');
        return;
      }

      // 使用单独的 SQL 语句而不是事务，以避免可能的事务问题
      // 插入支出分类
      for (const category of expenseCategories) {
        await this.database!.executeSql(
          'INSERT INTO categories (name, isExpense, icon) VALUES (?, ?, ?)',
          [category.name, category.isExpense ? 1 : 0, category.icon],
        );
      }

      // 插入收入分类
      for (const category of incomeCategories) {
        await this.database!.executeSql(
          'INSERT INTO categories (name, isExpense, icon) VALUES (?, ?, ?)',
          [category.name, category.isExpense ? 1 : 0, category.icon],
        );
      }

      console.log('分类数据初始化成功');
    } catch (error) {
      console.error('初始化分类数据失败', error);
      throw error;
    }
  }

  // 获取所有分类
  async getAllCategories(): Promise<Category[]> {
    if (!this.database) {
      await this.initDatabase();
    }

    try {
      const [result] = await this.database!.executeSql(
        'SELECT * FROM categories',
      );
      const categories: Category[] = [];

      for (let i = 0; i < result.rows.length; i++) {
        const row = result.rows.item(i);
        categories.push({
          id: row.id ? row.id.toString() : (i + 1).toString(), // 使用数字 ID
          name: row.name,
          isExpense: row.isExpense === 1,
          icon: row.icon || 'snowflake', // 如果没有图标，使用默认的圆形图标
        });
      }

      return categories;
    } catch (error) {
      console.error('获取分类失败', error);
      throw error;
    }
  }

  // 添加交易记录
  async addTransaction(transaction: Transaction): Promise<string> {
    if (!this.database) {
      await this.initDatabase();
    }

    try {
      // 如果没有提供 categoryName，则尝试从分类 ID 获取
      if (!transaction.categoryName && transaction.categoryId) {
        try {
          const category = await this.getCategoryById(transaction.categoryId);
          if (category) {
            transaction.categoryName = category.name;
          } else {
            // 如果找不到分类，使用默认值
            transaction.categoryName =
              transaction.type === 'expense' ? '其他支出' : '其他收入';
          }
        } catch (error) {
          console.error('获取分类名称失败', error);
          // 使用默认值
          transaction.categoryName =
            transaction.type === 'expense' ? '其他支出' : '其他收入';
        }
      } else if (!transaction.categoryName) {
        // 如果没有提供 categoryName 且没有 categoryId，使用默认值
        transaction.categoryName =
          transaction.type === 'expense' ? '其他支出' : '其他收入';
      }
      const [result] = await this.database!.executeSql(
        'INSERT INTO transactions (amount, note, date, categoryId, categoryName, type, familyId, familyName, isReimbursable, accountBookId, accountBookName) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)',
        [
          transaction.amount,
          transaction.note,
          transaction.date,
          transaction.categoryId,
          transaction.categoryName, // 确保总是有值
          transaction.type,
          transaction.familyId,
          transaction.familyName,
          transaction.isReimbursable ? 1 : 0,
          transaction.accountBookId,
          transaction.accountBookName,
        ],
      );

      return result.insertId?.toString() || '';
    } catch (error) {
      console.error('添加交易记录失败:', error);
      throw error;
    }
  }

  // 获取所有交易记录
  async getAllTransactions(familyId): Promise<Transaction[]> {
    if (!this.database) {
      await this.initDatabase();
    }

    try {
      const sql = familyId
        ? 'SELECT * FROM transactions WHERE familyId = ? ORDER BY date DESC'
        : 'SELECT * FROM transactions ORDER BY date DESC';
      const [result] = await this.database!.executeSql(
        sql,
        familyId ? [familyId] : [],
      );
      const transactions: Transaction[] = [];

      for (let i = 0; i < result.rows.length; i++) {
        const row = result.rows.item(i);
        transactions.push({
          id: row.id,
          amount: row.amount,
          note: row.note,
          date: row.date,
          categoryId: row.categoryId,
          categoryName: row.categoryName,
          type: row.type,
          familyId: row.familyId,
          familyName: row.familyName,
          isReimbursable: row.isReimbursable === 1,
          accountBookId: row.accountBookId,
          accountBookName: row.accountBookName,
        });
      }

      return transactions;
    } catch (error) {
      console.error('获取交易记录失败', error);
      throw error;
    }
  }

  // 获取指定日期范围内的交易记录
  async getTransactionsByDateRange(
    startDate: string,
    endDate: string,
    familyId?: number,
  ): Promise<Transaction[]> {
    if (!this.database) {
      await this.initDatabase();
    }

    try {
      const sql = familyId
        ? 'SELECT * FROM transactions WHERE date >=? AND date <=? AND familyId =? ORDER BY date DESC'
        : 'SELECT * FROM transactions WHERE date >=? AND date <=? ORDER BY date DESC';
      const [result] = await this.database!.executeSql(
        sql,
        familyId ? [startDate, endDate, familyId] : [startDate, endDate],
      );
      const transactions: Transaction[] = [];

      for (let i = 0; i < result.rows.length; i++) {
        const row = result.rows.item(i);
        transactions.push({
          id: row.id,
          amount: row.amount,
          note: row.note,
          date: row.date,
          categoryId: row.categoryId,
          categoryName: row.categoryName,
          type: row.type,
          familyId: row.familyId,
          familyName: row.familyName,
          isReimbursable: row.isReimbursable === 1,
          accountBookId: row.accountBookId,
          accountBookName: row.accountBookName,
        });
      }

      return transactions;
    } catch (error) {
      console.error('获取日期范围内的交易记录失败', error);
      throw error;
    }
  }

  // 获取指定月份的交易记录
  async getTransactionsByMonth(
    year: number,
    month: number,
    familyId?: number,
  ): Promise<Transaction[]> {
    const startDate = `${year}-${month.toString().padStart(2, '0')}-01`;
    const endDate = `${year}-${month.toString().padStart(2, '0')}-31`;
    return this.getTransactionsByDateRange(startDate, endDate, familyId);
  }

  // 获取指定日期的交易记录
  async getTransactionsByDate(date: string): Promise<Transaction[]> {
    if (!this.database) {
      await this.initDatabase();
    }

    try {
      const [result] = await this.database!.executeSql(
        'SELECT * FROM transactions WHERE date = ? ORDER BY id DESC',
        [date],
      );
      const transactions: Transaction[] = [];

      for (let i = 0; i < result.rows.length; i++) {
        const row = result.rows.item(i);
        transactions.push({
          id: row.id,
          amount: row.amount,
          note: row.note,
          date: row.date,
          categoryId: row.categoryId,
          categoryName: row.categoryName,
          type: row.type,
          familyId: row.familyId,
          familyName: row.familyName,
          isReimbursable: row.isReimbursable === 1,
          accountBookId: row.accountBookId,
          accountBookName: row.accountBookName,
        });
      }

      return transactions;
    } catch (error) {
      console.error('获取指定日期的交易记录失败', error);
      throw error;
    }
  }

  // 删除交易记录
  async deleteTransaction(transactionId: number): Promise<void> {
    if (!this.database) {
      await this.initDatabase();
    }

    try {
      await this.database!.executeSql('DELETE FROM transactions WHERE id = ?', [
        transactionId,
      ]);

      console.log(`删除交易记录成功，ID: ${transactionId}`);
    } catch (error) {
      console.error('删除交易记录失败', error);
      throw error;
    }
  }

  // 更新交易记录
  async updateTransaction(transaction: Transaction): Promise<void> {
    if (!this.database) {
      await this.initDatabase();
    }
    try {
      if (!transaction.id) {
        throw new Error('交易记录ID不能为空');
      }
      // 准备 SQL 语句，包含新字段
      const sql = `
        UPDATE transactions
        SET amount = ?, note = ?, date = ?, categoryId = ?, categoryName = ?, type = ?, familyId = ?, familyName = ?, isReimbursable = ?, accountBookId = ?, accountBookName = ?
        WHERE id = ?
      `;

      // 执行 SQL，包含新字段的值
      await this.database!.executeSql(sql, [
        transaction.amount,
        transaction.note,
        transaction.date,
        transaction.categoryId,
        transaction.categoryName,
        transaction.type,
        transaction.familyId,
        transaction.familyName,
        transaction.isReimbursable ? 1 : 0,
        transaction.accountBookId,
        transaction.accountBookName,
        transaction.id,
      ]);
    } catch (error) {
      console.error('更新交易记录失败:', error);
      throw error;
    }
  }

  // 获取月度统计数据
  async getMonthlyStatistics(
    year: number,
    month: number,
    familyId?: number,
  ): Promise<{income: number; expense: number}> {
    if (!this.database) {
      await this.initDatabase();
    }

    const startDate = `${year}-${month.toString().padStart(2, '0')}-01`;
    const endDate = `${year}-${month.toString().padStart(2, '0')}-31`;

    const sql = familyId
      ? `
      SELECT SUM(amount) as total FROM transactions
      WHERE type =? AND date >=? AND date <=? AND familyId =?
      GROUP BY type
    `
      : `
      SELECT SUM(amount) as total FROM transactions
      WHERE type =? AND date >=? AND date <=?
      GROUP BY type
    `;
    try {
      // 获取收入总额
      const [incomeResult] = await this.database!.executeSql(
        sql,
        familyId
          ? ['income', startDate, endDate, familyId]
          : ['income', startDate, endDate],
      );
      const income = incomeResult.rows.item(0)?.total || 0;

      // 获取支出总额
      const [expenseResult] = await this.database!.executeSql(
        sql,
        familyId
          ? ['expense', startDate, endDate, familyId]
          : ['expense', startDate, endDate],
      );
      const expense = expenseResult.rows.item(0)?.total || 0;

      return {income, expense};
    } catch (error) {
      console.error('获取月度统计数据失败', error);
      throw error;
    }
  }

  // 获取所有有交易记录的日期
  async getTransactionDates(): Promise<string[]> {
    if (!this.database) {
      await this.initDatabase();
    }

    try {
      // 使用 GROUP BY 查询所有不同的日期
      const result = await this.database!.executeSql(
        'SELECT DISTINCT date FROM transactions ORDER BY date DESC',
      );

      const dates: string[] = [];
      for (let i = 0; i < result[0].rows.length; i++) {
        const item = result[0].rows.item(i);
        dates.push(item.date);
      }

      console.log('获取到的交易日期:', dates);
      return dates;
    } catch (error) {
      console.error('获取交易日期失败', error);
      return [];
    }
  }

  // 关闭数据库
  async closeDatabase(): Promise<void> {
    if (this.database) {
      await this.database.close();
      this.database = null;
      this.initialized = false;
    }
  }

  // 清除所有数据
  async clearAllData(): Promise<void> {
    try {
      if (!this.database) {
        throw new Error('数据库未初始化');
      }

      await this.database.transaction(tx => {
        // 删除现有表
        tx.executeSql('DROP TABLE IF EXISTS transactions');
        tx.executeSql('DROP TABLE IF EXISTS categories');
        tx.executeSql('DROP TABLE IF EXISTS settings');
        tx.executeSql('DROP TABLE IF EXISTS credit_cards');
        tx.executeSql('DROP TABLE IF EXISTS category_notes');
        tx.executeSql('DROP TABLE IF EXISTS installment_plans');
        tx.executeSql('DROP TABLE IF EXISTS installment_details');
        tx.executeSql('DROP TABLE IF EXISTS products');
        tx.executeSql('DROP TABLE IF EXISTS product_prices');

        // 删除家庭相关表
        tx.executeSql('DROP TABLE IF EXISTS family_members');
        tx.executeSql('DROP TABLE IF EXISTS families');

        // 删除借款相关表
        tx.executeSql('DROP TABLE IF EXISTS loan_records');
        tx.executeSql('DROP TABLE IF EXISTS loan_repayments');
      });

      // 重新创建表
      await this.createTables();
      await this.createInstallmentTables();
      await this.createFamilyTables();
      await this.createLoanTable(this.database);

      // 重新初始化默认分类
      await this.initDefaultCategories();
    } catch (error) {
      console.error('清除所有数据失败', error);
      throw error;
    }
  }

  // 在 DatabaseService 类中添加数据库迁移方法
  private async migrateDatabase(): Promise<void> {
    try {
      // 检查是否存在 icon 列
      const [result] = await this.database!.executeSql(
        'PRAGMA table_info(categories)',
      );

      let hasIconColumn = false;
      for (let i = 0; i < result.rows.length; i++) {
        const column = result.rows.item(i);
        if (column.name === 'icon') {
          hasIconColumn = true;
          break;
        }
      }

      // 如果没有 icon 列，添加它
      if (!hasIconColumn) {
        await this.database!.executeSql(
          'ALTER TABLE categories ADD COLUMN icon TEXT',
        );

        // 更新现有分类的图标
        // 支出分类
        await this.database!.executeSql(
          "UPDATE categories SET icon = 'utensils' WHERE name = '餐饮' AND isExpense = 1",
        );
        await this.database!.executeSql(
          "UPDATE categories SET icon = 'cart-shopping' WHERE name = '购物' AND isExpense = 1",
        );
        // ... 其他分类更新

        // 默认图标
        await this.database!.executeSql(
          "UPDATE categories SET icon = 'snowflake' WHERE icon IS NULL",
        );
      }

      console.log('数据库迁移成功');
    } catch (error) {
      console.error('数据库迁移失败', error);
      throw error;
    }
  }

  // 添加验证分类数据完整性的方法
  async validateCategories(): Promise<void> {
    if (!this.database) {
      await this.initDatabase();
    }

    try {
      // 检查分类数量
      const [result] = await this.database!.executeSql(
        'SELECT COUNT(*) as count FROM categories',
      );
      const count = result.rows.item(0).count;

      // 检查是否有分类缺少 ID
      const [idCheckResult] = await this.database!.executeSql(
        'SELECT COUNT(*) as count FROM categories WHERE id IS NULL',
      );
      const nullIdCount = idCheckResult.rows.item(0).count;

      // 如果有分类缺少 ID，修复它们
      if (nullIdCount > 0) {
        console.warn(`发现 ${nullIdCount} 个分类缺少 ID，尝试修复...`);

        // 获取所有缺少 ID 的分类
        const [nullIdCategories] = await this.database!.executeSql(
          'SELECT rowid, * FROM categories WHERE id IS NULL',
        );

        // 使用 rowid 更新这些分类的 ID
        for (let i = 0; i < nullIdCategories.rows.length; i++) {
          const category = nullIdCategories.rows.item(i);
          await this.database!.executeSql(
            'UPDATE categories SET id = ? WHERE rowid = ?',
            [category.rowid, category.rowid],
          );
        }

        console.log('已修复缺少 ID 的分类');
      }

      // 如果分类数量少于预期（支出12个+收入7个=19个），重新初始化
      if (count < 19) {
        console.warn(`分类数据不完整，只有 ${count} 个分类，重新初始化...`);

        // 删除所有分类
        await this.database!.executeSql('DELETE FROM categories');

        // 重置分类表的自增 ID
        await this.database!.executeSql(
          'DELETE FROM sqlite_sequence WHERE name = ?',
          ['categories'],
        );

        // 重新初始化分类
        await this.initializeCategories();

        console.log('分类数据已重新初始化');
      } else {
        console.log(`分类数据完整，共有 ${count} 个分类`);
      }
    } catch (error) {
      console.error('验证分类数据失败', error);
      throw error;
    }
  }

  // 添加新分类
  async addCategory(category: Category): Promise<string> {
    // 确保数据库连接是最新的
    await this.reinitDatabase();

    try {
      const [result] = await this.database!.executeSql(
        'INSERT INTO categories (name, isExpense, icon) VALUES (?, ?, ?)',
        [category.name, category.isExpense ? 1 : 0, category.icon],
      );

      const id = result.insertId?.toString() || '';
      console.log(`添加分类成功，ID: ${id}`);
      return id;
    } catch (error) {
      console.error('添加分类失败', error);
      throw error;
    }
  }

  // 删除分类
  async deleteCategory(categoryId: string): Promise<void> {
    // 确保数据库连接是最新的
    await this.reinitDatabase();

    try {
      const categoryIdNum = categoryId.toString();
      console.log(`开始删除分类，ID: ${categoryId}, 转换后: ${categoryIdNum}`);

      // 首先检查分类是否存在
      const [checkCategoryResult] = await this.database!.executeSql(
        'SELECT * FROM categories WHERE id = ?',
        [categoryIdNum],
      );

      console.log(
        '检查分类是否存在，查询结果',
        JSON.stringify(checkCategoryResult.rows.item(0)),
      );
      if (checkCategoryResult.rows.length === 0) {
        console.error(`分类不存在，ID: ${categoryId}`);

        // 尝试使用字符串 ID 查询
        const [checkStringResult] = await this.database!.executeSql(
          'SELECT * FROM categories WHERE id = ?',
          [categoryId],
        );

        if (checkStringResult.rows.length === 0) {
          throw new Error('分类不存在');
        } else {
          console.log(`使用字符串 ID 找到分类: ${categoryId}`);
        }
      }

      // 首先检查是否有使用此分类的交易记录
      const [checkResult] = await this.database!.executeSql(
        'SELECT COUNT(*) as count FROM transactions WHERE categoryId = ?',
        [categoryId], // 使用原始 ID，因为交易记录中可能存储的是字符串
      );

      const count = checkResult.rows.item(0).count;
      console.log(`该分类下有 ${count} 条交易记录`);

      if (count > 0) {
        // 首先获取要删除的分类的类型（支出/收入）
        const [typeResult] = await this.database!.executeSql(
          'SELECT isExpense FROM categories WHERE id = ? OR id = ?',
          [categoryIdNum, categoryId], // 尝试两种类型的 ID
        );

        if (typeResult.rows.length === 0) {
          throw new Error('无法获取分类类型');
        }

        const isExpense = typeResult.rows.item(0).isExpense;
        console.log(`分类类型 isExpense: ${isExpense}`);

        // 然后找到对应类型的"其他"分类
        const [categoryResult] = await this.database!.executeSql(
          'SELECT id FROM categories WHERE name = ? AND isExpense = ?',
          ['其他', isExpense],
        );

        if (categoryResult.rows.length > 0) {
          const otherCategoryId = categoryResult.rows.item(0).id;
          console.log(`将交易记录转移到"其他"分类，ID: ${otherCategoryId}`);

          // 更新交易记录
          await this.database!.executeSql(
            'UPDATE transactions SET categoryId = ? WHERE categoryId = ?',
            [otherCategoryId, categoryId],
          );
        }
      }

      // 尝试使用数字和字符串两种方式删除
      const [deleteResult1] = await this.database!.executeSql(
        'DELETE FROM categories WHERE id = ?',
        [categoryIdNum],
      );

      console.log(
        `使用数字 ID 删除结果，影响行数: ${deleteResult1.rowsAffected}`,
      );

      if (deleteResult1.rowsAffected === 0) {
        // 如果使用数字 ID 删除失败，尝试使用字符串 ID
        const [deleteResult2] = await this.database!.executeSql(
          'DELETE FROM categories WHERE id = ?',
          [categoryId],
        );

        console.log(
          `使用字符串 ID 删除结果，影响行数: ${deleteResult2.rowsAffected}`,
        );

        if (deleteResult2.rowsAffected === 0) {
          throw new Error('删除分类失败，没有记录被删除');
        }
      }

      console.log(`删除分类成功，ID: ${categoryId}`);
    } catch (error) {
      console.error('删除分类失败', error);
      throw error;
    }
  }

  // 获取所有有交易记录的年份
  async getTransactionYears(familyId = ''): Promise<number[]> {
    if (!this.database) {
      await this.initDatabase();
    }
    const sql = familyId
      ? 'SELECT DISTINCT strftime("%Y", date) as year FROM transactions WHERE familyId = ? ORDER BY year DESC'
      : 'SELECT DISTINCT strftime("%Y", date) as year FROM transactions ORDER BY year DESC';
    try {
      // 使用 SQL 查询获取所有不同的年份
      const result = await this.database!.executeSql(
        sql,
        familyId ? [familyId] : [],
      );

      const years: number[] = [];
      for (let i = 0; i < result[0].rows.length; i++) {
        const item = result[0].rows.item(i);
        console.log('---item----', item);
        years.push(parseInt(item.year, 10));
      }

      // 如果没有数据，返回当前年份
      if (years.length === 0) {
        return [new Date().getFullYear()];
      }

      return years;
    } catch (error) {
      console.error('获取交易年份失败', error);
      return [new Date().getFullYear()];
    }
  }

  // 添加获取特定类别和月份的交易记录方法
  async getTransactionsByCategoryAndMonth(
    categoryId: number,
    year: number,
    month: number,
    familyId?: number,
  ): Promise<Transaction[]> {
    try {
      const startDate = `${year}-${month.toString().padStart(2, '0')}-01`;
      const lastDay = new Date(year, month, 0).getDate();
      const endDate = `${year}-${month.toString().padStart(2, '0')}-${lastDay
        .toString()
        .padStart(2, '0')}`;
      const sql = familyId
        ? `
        SELECT * FROM transactions
        WHERE categoryId =?
        AND date BETWEEN? AND?
        AND familyId =?
        ORDER BY date DESC, id DESC
      `
        : `
        SELECT * FROM transactions
        WHERE categoryId =?
        AND date BETWEEN? AND?
        ORDER BY date DESC, id DESC
      `;
      const result = await this.database!.executeSql(
        sql,
        familyId
          ? [categoryId, startDate, endDate, familyId]
          : [categoryId, startDate, endDate],
      );

      return this.mapTransactionsFromResults(result);
    } catch (error) {
      console.error('获取类别月度交易记录失败', error);
      return [];
    }
  }

  // 添加 mapTransactionsFromResults 方法
  private mapTransactionsFromResults(result: any): Transaction[] {
    const transactions: Transaction[] = [];

    if (result && result.length > 0) {
      const rows = result[0].rows;
      for (let i = 0; i < rows.length; i++) {
        const item = rows.item(i);
        transactions.push({
          id: item.id,
          amount: item.amount,
          type: item.type,
          categoryId: item.categoryId,
          categoryName: item.categoryName,
          date: item.date,
          note: item.note,
          familyId: item.familyId,
          familyName: item.familyName,
          isReimbursable: item.isReimbursable === 1,
          accountBookId: item.accountBookId,
          accountBookName: item.accountBookName,
        });
      }
    }

    return transactions;
  }

  // 修改搜索交易的方法，支持分类名称搜索
  async searchTransactions(
    query: string,
    familyId?: number,
    offset: number = 0,
    limit: number = 30,
    isReimbursable?: boolean,
  ): Promise<Transaction[]> {
    try {
      if (!this.database) {
        await this.initDatabase();
      }

      // 构建搜索条件
      const searchTerm = `%${query}%`;
      console.log('搜索条件：', searchTerm);
      console.log('筛选条件 - 待报销:', isReimbursable);

      // 首先获取所有分类
      const categories = await this.getAllCategories();

      // 找到匹配查询的分类ID
      const matchingCategoryIds = categories
        .filter(category =>
          category.name.toLowerCase().includes(query.toLowerCase()),
        )
        .map(category => category.id);

      let transactions: Transaction[] = [];

      // 1. 搜索类型、备注和金额
      let sql = '';
      let params: any[] = [];

      // 如果有搜索文本
      if (query.trim()) {
        if (familyId !== undefined) {
          sql = `
            SELECT * FROM transactions
            WHERE (type LIKE ? OR note LIKE ? OR amount LIKE ? OR familyName LIKE ?)
            AND familyId = ?
          `;
          params = [searchTerm, searchTerm, searchTerm, searchTerm, familyId];
        } else {
          sql = `
            SELECT * FROM transactions
            WHERE (type LIKE ? OR note LIKE ? OR amount LIKE ? OR familyName LIKE ?)
          `;
          params = [searchTerm, searchTerm, searchTerm, searchTerm];
        }
      } else {
        // 没有搜索文本，只筛选
        if (familyId !== undefined) {
          sql = 'SELECT * FROM transactions WHERE familyId = ?';
          params = [familyId];
        } else {
          sql = 'SELECT * FROM transactions WHERE 1=1';
          params = [];
        }
      }

      // 添加待报销筛选条件
      if (isReimbursable !== undefined) {
        sql += ' AND isReimbursable = ?';
        params.push(isReimbursable ? 1 : 0);
      }

      // 添加排序和分页
      sql += ' ORDER BY date DESC, id DESC LIMIT ? OFFSET ?';
      params.push(limit, offset);

      console.log('执行SQL:', sql);
      console.log('参数:', params);

      const result1 = await this.database!.executeSql(sql, params);
      transactions = this.mapTransactionsFromResults(result1);

      // 2. 如果有匹配的分类且有搜索关键词，也搜索这些分类的交易
      if (matchingCategoryIds.length > 0 && query.trim()) {
        // 构建 IN 查询的参数占位符
        const placeholders = matchingCategoryIds.map(() => '?').join(',');
        let categorySql = `
          SELECT * FROM transactions 
          WHERE categoryId IN (${placeholders})
        `;

        let categoryParams: any[] = [...matchingCategoryIds];

        // 添加家庭ID筛选
        if (familyId !== undefined) {
          categorySql += ' AND familyId = ?';
          categoryParams.push(familyId);
        }

        // 添加待报销筛选
        if (isReimbursable !== undefined) {
          categorySql += ' AND isReimbursable = ?';
          categoryParams.push(isReimbursable ? 1 : 0);
        }

        // 添加排序和分页
        categorySql += ' ORDER BY date DESC, id DESC LIMIT ? OFFSET ?';
        categoryParams.push(limit, offset);

        console.log('执行分类SQL:', categorySql);
        console.log('分类参数:', categoryParams);

        const result2 = await this.database!.executeSql(categorySql, categoryParams);

        const categoryTransactions = this.mapTransactionsFromResults(result2);

        // 合并结果并去重
        const allTransactions = [...transactions, ...categoryTransactions];
        const uniqueTransactions = Array.from(
          new Map(allTransactions.map(item => [item.id, item])).values(),
        );

        transactions = uniqueTransactions;
      }

      return transactions;
    } catch (error) {
      console.error('搜索交易失败', error);
      return [];
    }
  }

  // 添加创建表的方法
  async createTables(): Promise<void> {
    try {
      // 创建账本表
      await this.database!.executeSql(`
        CREATE TABLE IF NOT EXISTS account_books (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          name TEXT NOT NULL,
          description TEXT,
          isDefault INTEGER DEFAULT 0,
          createdAt TEXT NOT NULL,
          updatedAt TEXT
        );
      `);

      // 创建交易表
      await this.database!.executeSql(`
        CREATE TABLE IF NOT EXISTS transactions (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          amount REAL NOT NULL,
          note TEXT,
          date TEXT NOT NULL,
          categoryId TEXT NOT NULL,
          categoryName TEXT NOT NULL,
          type TEXT NOT NULL,
          familyId INTEGER DEFAULT NULL,
          familyName TEXT,
          isReimbursable INTEGER DEFAULT 0,
          accountBookId INTEGER DEFAULT NULL,
          accountBookName TEXT
        );
      `);

      // 检查列是否已存在
      try {
        const [result] = await this.database!.executeSql(
          'PRAGMA table_info(transactions)',
        );
        let hasColumn = false;

        for (let i = 0; i < result.rows.length; i++) {
          const column = result.rows.item(i);
          if (column.name === 'isReimbursable') {
            hasColumn = true;
            break;
          }
        }

        if (!hasColumn) {
          await this.database!.executeSql(
            'ALTER TABLE transactions ADD COLUMN isReimbursable INTEGER DEFAULT 0',
          );
          console.log('成功添加 isReimbursable 列');
        } else {
          console.log('isReimbursable 列已存在，无需添加');
        }
      } catch (error) {
        console.error('添加列失败:', error);
        // 显示详细错误信息
        console.error('错误代码:', error.code);
        console.error('错误消息:', error.message);
      }

      // 创建分类表
      await this.database!.executeSql(`
        CREATE TABLE IF NOT EXISTS categories (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          name TEXT NOT NULL,
          isExpense INTEGER NOT NULL,
          icon TEXT
        );
      `);

      // 创建设置表
      await this.database!.executeSql(`
        CREATE TABLE IF NOT EXISTS settings (
          key TEXT PRIMARY KEY,
          value TEXT NOT NULL
        );
      `);

      // 创建分期账单表
      await this.database!.executeSql(`
        CREATE TABLE IF NOT EXISTS installment_plans (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          name TEXT NOT NULL,
          type TEXT NOT NULL,
          start_date TEXT NOT NULL,
          total_amount REAL NOT NULL,
          down_payment REAL DEFAULT 0,
          number_of_installments INTEGER NOT NULL,
          installment_mode TEXT NOT NULL,
          annual_rate REAL DEFAULT 0,
          handling_fee REAL DEFAULT 0,
          auto_record INTEGER DEFAULT 1,
          created_at TEXT NOT NULL,
          updated_at TEXT
        );
      `);

      // 创建分期详情表
      await this.database!.executeSql(`
        CREATE TABLE IF NOT EXISTS installment_details (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          plan_id INTEGER NOT NULL,
          installment_number INTEGER NOT NULL, 
          due_date TEXT NOT NULL,
          amount REAL NOT NULL,
          is_paid INTEGER DEFAULT 0,
          payment_date TEXT,
          transaction_id INTEGER, 
          FOREIGN KEY (plan_id) REFERENCES installment_plans (id) ON DELETE CASCADE
        );
      `);

      console.log('表创建成功');
    } catch (error) {
      console.error('创建表失败', error);
      throw error;
    }
  }

  // 添加初始化默认分类的方法
  async initDefaultCategories(): Promise<void> {
    try {
      // 检查分类表是否有数据
      const [result] = await this.database!.executeSql(
        'SELECT COUNT(*) as count FROM categories',
      );
      const count = result.rows.item(0).count;

      if (count === 0) {
        // 如果没有分类数据，初始化默认分类
        await this.initializeCategories();
      } else {
        console.log('分类数据已存在，跳过初始化');
      }
    } catch (error) {
      console.error('初始化默认分类失败', error);
      throw error;
    }
  }

  // 添加获取指定年份所有交易的方法
  async getYearTransactions(year) {
    try {
      const startDate = `${year}-01-01`;
      const endDate = `${year}-12-31`;

      const query = `
        SELECT 
          t.*,
          c.name as categoryName,
          c.icon as categoryIcon,
          c.color as categoryColor
        FROM transactions t
        LEFT JOIN categories c ON t.categoryId = c.id
        WHERE t.date >= ? AND t.date <= ?
        ORDER BY t.date DESC
      `;

      const result = await this.executeSql(query, [startDate, endDate]);

      return this.processTransactionResults(result);
    } catch (error) {
      console.error('获取年度交易记录失败', error);
      return [];
    }
  }

  // 保存预算设置
  async saveBudgetSettings(settings: BudgetSettings): Promise<void> {
    try {
      // 确保数据库已初始化
      if (!this.database) {
        await this.initDatabase();
      }

      // 确保 settings 表存在
      await this.createSettingsTableIfNotExists();

      await this.database!.executeSql(
        'INSERT OR REPLACE INTO settings (key, value) VALUES (?, ?)',
        ['budget_settings', JSON.stringify(settings)],
      );
    } catch (error) {
      console.error('保存预算设置失败', error);
      throw error;
    }
  }

  // 获取预算设置
  async getBudgetSettings(): Promise<BudgetSettings> {
    try {
      // 确保数据库已初始化
      if (!this.database) {
        await this.initDatabase();
      }

      // 确保 settings 表存在
      await this.createSettingsTableIfNotExists();

      const [result] = await this.database!.executeSql(
        'SELECT value FROM settings WHERE key = ?',
        ['budget_settings'],
      );

      if (result.rows.length > 0) {
        const settings = JSON.parse(result.rows.item(0).value);
        return settings;
      } else {
        // 返回默认设置
        return {
          daily: '',
          monthly: '',
          yearly: '',
          enabled: false,
        };
      }
    } catch (error) {
      console.error('获取预算设置失败', error);
      // 返回默认设置
      return {
        daily: '',
        monthly: '',
        yearly: '',
        enabled: false,
      };
    }
  }

  // 保存自动导出设置
  async saveAutoExportSettings(settings: AutoExportSettings): Promise<void> {
    try {
      // 确保数据库已初始化
      if (!this.database) {
        await this.initDatabase();
      }

      // 确保 settings 表存在
      await this.createSettingsTableIfNotExists();

      await this.database!.executeSql(
        'INSERT OR REPLACE INTO settings (key, value) VALUES (?, ?)',
        ['auto_export_settings', JSON.stringify(settings)],
      );
    } catch (error) {
      console.error('保存自动导出设置失败', error);
      throw error;
    }
  }

  // 获取自动导出设置
  async getAutoExportSettings(): Promise<AutoExportSettings> {
    try {
      // 确保数据库已初始化
      if (!this.database) {
        await this.initDatabase();
      }

      // 确保 settings 表存在
      await this.createSettingsTableIfNotExists();

      const [result] = await this.database!.executeSql(
        'SELECT value FROM settings WHERE key = ?',
        ['auto_export_settings'],
      );

      if (result.rows.length > 0) {
        const settings = JSON.parse(result.rows.item(0).value);
        return settings;
      } else {
        // 返回默认设置
        return {
          enabled: false,
          lastExportDate: undefined,
        };
      }
    } catch (error) {
      console.error('获取自动导出设置失败', error);
      // 返回默认设置
      return {
        enabled: false,
        lastExportDate: undefined,
      };
    }
  }

  // 更新最后导出日期
  async updateLastExportDate(): Promise<void> {
    try {
      const settings = await this.getAutoExportSettings();
      settings.lastExportDate = dayjs().format('YYYY-MM-DD');
      await this.saveAutoExportSettings(settings);
    } catch (error) {
      console.error('更新最后导出日期失败', error);
      throw error;
    }
  }

  // 检查是否需要自动导出
  async shouldAutoExport(): Promise<boolean> {
    try {
      const settings = await this.getAutoExportSettings();

      // 如果自动导出功能未启用，则不需要导出
      if (!settings.enabled) {
        return false;
      }

      const today = dayjs();

      // 如果没有上次导出日期，则需要导出
      if (!settings.lastExportDate) {
        return true;
      }

      // 获取当前年月和上次导出的年月
      const currentYearMonth = today.format('YYYY-MM');
      const lastExportYearMonth = dayjs(settings.lastExportDate).format(
        'YYYY-MM',
      );

      // 如果当前年月与上次导出的年月不同，则需要导出（每月导出一次）
      if (currentYearMonth !== lastExportYearMonth) {
        console.log('当月未导出过数据，需要自动导出');
        return true;
      }

      // 当月已导出过，不需要再次导出
      console.log('当月已导出过数据，不需要再次导出');
      return false;
    } catch (error) {
      console.error('检查是否需要自动导出失败', error);
      return false;
    }
  }

  // 创建 settings 表（如果不存在）
  async createSettingsTableIfNotExists(): Promise<void> {
    try {
      await this.database!.executeSql(`
        CREATE TABLE IF NOT EXISTS settings (
          key TEXT PRIMARY KEY,
          value TEXT NOT NULL
        );
      `);
    } catch (error) {
      console.error('创建设置表失败', error);
      throw error;
    }
  }

  // 检查是否超出预算
  async checkBudgetExceeded({amount, date}): Promise<{
    exceeded: boolean;
    type?: 'daily' | 'monthly' | 'yearly';
    budget?: string;
    actual?: number;
  }> {
    try {
      const settings = await this.getBudgetSettings();
      const startOfMonth = dayjs(date).startOf('month').format('YYYY-MM-DD');
      const endOfMonth = dayjs(date).endOf('month').format('YYYY-MM-DD');
      const startOfYear = dayjs(date).startOf('year').format('YYYY-MM-DD');
      const endOfYear = dayjs(date).endOf('year').format('YYYY-MM-DD');
      if (!settings || !settings.enabled) {
        return {exceeded: false};
      }
      // 获取今日支出总额
      const dailyExpenses = await this.getTotalExpensesByDateRange(date, date);
      console.log('--dailyExpenses', dailyExpenses, date);

      // 获取本月支出总额
      const monthlyExpenses = await this.getTotalExpensesByDateRange(
        startOfMonth,
        endOfMonth,
      );
      console.log(
        '--monthlyExpenses',
        monthlyExpenses,
        startOfMonth,
        endOfMonth,
      );

      // 获取本年支出总额
      const yearlyExpenses = await this.getTotalExpensesByDateRange(
        startOfYear,
        endOfYear,
      );
      console.log('--yearlyExpenses', yearlyExpenses, startOfYear, endOfYear);

      // 检查是否超出年预算
      if (settings.yearly && parseFloat(settings.yearly) > 0) {
        const yearlyBudget = parseFloat(settings.yearly);
        const newYearlyTotal = yearlyExpenses + amount;

        if (newYearlyTotal > yearlyBudget) {
          return {
            exceeded: true,
            type: 'yearly',
            budget: settings.yearly,
            actual: newYearlyTotal,
          };
        }
      }
      // 检查是否超出月预算
      if (settings.monthly && parseFloat(settings.monthly) > 0) {
        const monthlyBudget = parseFloat(settings.monthly);
        const newMonthlyTotal = monthlyExpenses + amount;

        if (newMonthlyTotal > monthlyBudget) {
          return {
            exceeded: true,
            type: 'monthly',
            budget: settings.monthly,
            actual: newMonthlyTotal,
          };
        }
      }

      // 检查是否超出日预算
      if (settings.daily && parseFloat(settings.daily) > 0) {
        const dailyBudget = parseFloat(settings.daily);
        const newDailyTotal = dailyExpenses + amount;

        if (newDailyTotal > dailyBudget) {
          return {
            exceeded: true,
            type: 'daily',
            budget: settings.daily,
            actual: newDailyTotal,
          };
        }
      }

      return {exceeded: false};
    } catch (error) {
      console.error('检查预算超额失败', error);
      return {exceeded: false};
    }
  }

  // 获取指定日期范围内的支出总额
  async getTotalExpensesByDateRange(
    startDate: string,
    endDate: string,
  ): Promise<number> {
    try {
      const [result] = await this.database!.executeSql(
        'SELECT SUM(amount) as total FROM transactions WHERE type = ? AND date BETWEEN ? AND ?',
        ['expense', startDate, endDate],
      );
      const total = result.rows.item(0).total || 0;
      return total;
    } catch (error) {
      console.error('获取日期范围内支出总额失败', error);
      return 0;
    }
  }

  // 添加信用卡
  async addCreditCard(creditCard: CreditCard): Promise<string> {
    try {
      const id = Date.now().toString();
      const now = dayjs().format('YYYY-MM-DD HH:mm:ss');

      const newCard = {
        ...creditCard,
        id,
        createdAt: now,
        updatedAt: now,
        color: creditCard.color || COLORS.primary, // 默认使用主题色
      };

      await this.database!.executeSql(
        'INSERT INTO credit_cards (id, bank_name, last_three_digits, billing_day, payment_due_day, color, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?, ?, ?)',
        [
          newCard.id,
          newCard.bankName,
          newCard.lastThreeDigits,
          newCard.billingDay,
          newCard.paymentDueDay,
          newCard.color,
          newCard.createdAt,
          newCard.updatedAt,
        ],
      );

      return id;
    } catch (error) {
      console.error('添加信用卡失败', error);
      throw error;
    }
  }

  // 获取所有信用卡
  async getAllCreditCards(): Promise<CreditCard[]> {
    try {
      const [results] = await this.database!.executeSql(
        'SELECT * FROM credit_cards ORDER BY created_at DESC',
      );

      const cards: CreditCard[] = [];
      for (let i = 0; i < results.rows.length; i++) {
        const row = results.rows.item(i);
        cards.push({
          id: row.id,
          bankName: row.bank_name,
          lastThreeDigits: row.last_three_digits,
          billingDay: row.billing_day,
          paymentDueDay: row.payment_due_day,
          color: row.color,
          createdAt: row.created_at,
          updatedAt: row.updated_at,
        });
      }

      return cards;
    } catch (error) {
      console.error('获取信用卡列表失败', error);
      throw error;
    }
  }

  // 通过ID获取信用卡
  async getCreditCardById(id: string): Promise<CreditCard | null> {
    try {
      const [results] = await this.database!.executeSql(
        'SELECT * FROM credit_cards WHERE id = ?',
        [id],
      );

      if (results.rows.length === 0) {
        return null;
      }

      const row = results.rows.item(0);
      return {
        id: row.id,
        bankName: row.bank_name,
        lastThreeDigits: row.last_three_digits,
        billingDay: row.billing_day,
        paymentDueDay: row.payment_due_day,
        color: row.color,
        createdAt: row.created_at,
        updatedAt: row.updated_at,
      };
    } catch (error) {
      console.error('获取信用卡详情失败', error);
      throw error;
    }
  }

  // 更新信用卡
  async updateCreditCard(creditCard: CreditCard): Promise<void> {
    try {
      const now = dayjs().format('YYYY-MM-DD HH:mm:ss');

      await this.database!.executeSql(
        'UPDATE credit_cards SET bank_name = ?, last_three_digits = ?, billing_day = ?, payment_due_day = ?, color = ?, updated_at = ? WHERE id = ?',
        [
          creditCard.bankName,
          creditCard.lastThreeDigits,
          creditCard.billingDay,
          creditCard.paymentDueDay,
          creditCard.color,
          now,
          creditCard.id,
        ],
      );
    } catch (error) {
      console.error('更新信用卡失败', error);
      throw error;
    }
  }

  // 删除信用卡
  async deleteCreditCard(id: string): Promise<void> {
    try {
      await this.database!.executeSql('DELETE FROM credit_cards WHERE id = ?', [
        id,
      ]);
    } catch (error) {
      console.error('删除信用卡失败', error);
      throw error;
    }
  }

  // 重置数据库账单方法
  async resetBillDatabase(): Promise<void> {
    if (!this.database) {
      await this.initDatabase();
    }

    try {
      // 删除并重建所有表
      await this.database!.transaction(tx => {
        // 删除现有的表
        tx.executeSql('DROP TABLE IF EXISTS transactions');
      });

      // 重新创建所有表并初始化
      await this.createTables();
    } catch (error) {
      console.error('重置表单数据库失败:', error);
      throw error;
    }
  }

  // 重置数据库方法
  async resetDatabase(): Promise<void> {
    if (!this.database) {
      await this.initDatabase();
    }

    try {
      // 删除并重建所有表
      await this.database!.transaction(tx => {
        // 删除现有的表
        tx.executeSql('DROP TABLE IF EXISTS transactions');
        tx.executeSql('DROP TABLE IF EXISTS categories');
        tx.executeSql('DROP TABLE IF EXISTS settings');
        tx.executeSql('DROP TABLE IF EXISTS credit_cards');
        tx.executeSql('DROP TABLE IF EXISTS category_notes');
        tx.executeSql('DROP TABLE IF EXISTS installment_plans');
        tx.executeSql('DROP TABLE IF EXISTS installment_details');
        tx.executeSql('DROP TABLE IF EXISTS products');
        tx.executeSql('DROP TABLE IF EXISTS product_prices');

        // 删除家庭相关表
        tx.executeSql('DROP TABLE IF EXISTS family_members');
        tx.executeSql('DROP TABLE IF EXISTS families');

        // 删除借款相关表
        tx.executeSql('DROP TABLE IF EXISTS loan_records');
        tx.executeSql('DROP TABLE IF EXISTS loan_repayments');
      });

      // 重新创建所有表并初始化
      await this.createTables();
      await this.createInstallmentTables();
      await this.createFamilyTables();
      await this.createProductTables();
      // 重新初始化默认分类
      await this.initDefaultCategories();
      console.log('数据库已重置');
    } catch (error) {
      console.error('重置数据库失败:', error);
      throw error;
    }
  }

  // 保存分类的历史备注
  async saveCategoryNote(categoryId: string, note: string): Promise<void> {
    try {
      if (!this.database) {
        await this.initDatabase();
      }

      // 创建分类备注历史表（如果不存在）
      await this.database!.executeSql(`
        CREATE TABLE IF NOT EXISTS category_notes (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          category_id TEXT NOT NULL,
          note TEXT NOT NULL,
          created_at TEXT NOT NULL,
          UNIQUE(category_id, note) ON CONFLICT REPLACE
        );
      `);

      // 保存备注（替换重复的备注）
      await this.database!.executeSql(
        'INSERT OR REPLACE INTO category_notes (category_id, note, created_at) VALUES (?, ?, ?)',
        [categoryId, note, dayjs().format('YYYY-MM-DD HH:mm:ss')],
      );
    } catch (error) {
      console.error('保存分类备注失败', error);
    }
  }

  // 获取分类的历史备注
  async getCategoryNotes(categoryId: string): Promise<string[]> {
    try {
      if (!this.database) {
        await this.initDatabase();
      }

      // 创建分类备注历史表（如果不存在）
      await this.database!.executeSql(`
        CREATE TABLE IF NOT EXISTS category_notes (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          category_id TEXT NOT NULL,
          note TEXT NOT NULL,
          created_at TEXT NOT NULL,
          UNIQUE(category_id, note) ON CONFLICT REPLACE
        );
      `);

      // 获取最近的10条备注
      const [result] = await this.database!.executeSql(
        'SELECT note FROM category_notes WHERE category_id = ? ORDER BY created_at DESC LIMIT 10',
        [categoryId],
      );

      const notes: string[] = [];
      for (let i = 0; i < result.rows.length; i++) {
        notes.push(result.rows.item(i).note);
      }

      return notes;
    } catch (error) {
      console.error('获取分类备注失败', error);
      return [];
    }
  }

  // 添加分期账单
  async addInstallmentPlan(plan) {
    if (!this.database) {
      await this.initDatabase();
    }

    const {
      name,
      type,
      startDate,
      totalAmount,
      downPayment,
      numberOfInstallments,
      installmentMode,
      annualRate = 0,
      handlingFee = 0,
      autoRecord = 1, // 默认开启自动记账
    } = plan;

    // 插入分期计划主表记录
    const [result] = await this.database!.executeSql(
      `INSERT INTO installment_plans (
        name, type, start_date, total_amount, down_payment,
        number_of_installments, installment_mode, annual_rate, handling_fee, auto_record, created_at
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
      [
        name,
        type,
        startDate,
        totalAmount,
        downPayment || 0,
        numberOfInstallments,
        installmentMode,
        annualRate,
        handlingFee,
        autoRecord,
        dayjs().format('YYYY-MM-DD HH:mm:ss'),
      ],
    );

    const planId = result.insertId;

    // 计算每期金额和日期
    const amountAfterDownPayment = totalAmount - (downPayment || 0);
    const installmentAmount = this.calculateInstallmentAmount(
      amountAfterDownPayment,
      numberOfInstallments,
      installmentMode,
      annualRate,
      handlingFee,
    );

    // 插入每期详情
    for (let i = 1; i <= numberOfInstallments; i++) {
      const dueDate = this.calculateDueDate(startDate, i);
      await this.database!.executeSql(
        `INSERT INTO installment_details (
          plan_id, installment_number, due_date, amount, is_paid
        ) VALUES (?, ?, ?, ?, 0)`,
        [planId, i, dueDate, installmentAmount[i - 1]],
      );
    }

    return planId;
  }

  /**
   * 根据分期模式计算每期金额
   */
  calculateInstallmentAmount(
    totalAmount: number,
    numberOfInstallments: number,
    mode: string,
    annualRate: number = 0,
    handlingFee: number = 0,
  ): number[] {
    const amounts: number[] = [];
    const monthlyRate = annualRate / 100 / 12; // 月利率

    switch (mode) {
      case 'equal_payment': // 等额本息
        if (monthlyRate === 0) {
          // 无利息情况，等同于免息分期
          const baseAmount = Math.round((totalAmount / numberOfInstallments) * 100) / 100;
          for (let i = 0; i < numberOfInstallments; i++) {
            amounts.push(baseAmount);
          }
          // 处理四舍五入误差
          const sum = amounts.reduce((a, b) => a + b, 0);
          if (Math.abs(sum - totalAmount) > 0.01) {
            amounts[amounts.length - 1] += totalAmount - sum;
          }
        } else {
          // 等额本息公式：每期还款 = 本金 * [月利率 * (1+月利率)^期数] / [(1+月利率)^期数 - 1]
          const monthlyPayment = totalAmount *
            (monthlyRate * Math.pow(1 + monthlyRate, numberOfInstallments)) /
            (Math.pow(1 + monthlyRate, numberOfInstallments) - 1);

          for (let i = 0; i < numberOfInstallments; i++) {
            amounts.push(Math.round(monthlyPayment * 100) / 100);
          }
        }
        break;

      case 'equal_principal': // 等额本金
        const principalPerMonth = totalAmount / numberOfInstallments;
        let remainingPrincipal = totalAmount;

        for (let i = 0; i < numberOfInstallments; i++) {
          const interestPayment = remainingPrincipal * monthlyRate;
          const totalPayment = principalPerMonth + interestPayment;
          amounts.push(Math.round(totalPayment * 100) / 100);
          remainingPrincipal -= principalPerMonth;
        }
        break;

      case 'interest_first': // 先息后本
        const monthlyInterest = totalAmount * monthlyRate;

        // 前面期数只还利息
        for (let i = 0; i < numberOfInstallments - 1; i++) {
          amounts.push(Math.round(monthlyInterest * 100) / 100);
        }
        // 最后一期还本金+利息
        amounts.push(Math.round((totalAmount + monthlyInterest) * 100) / 100);
        break;

      case 'no_interest': // 免息分期
        const baseAmount = Math.round((totalAmount / numberOfInstallments) * 100) / 100;
        for (let i = 0; i < numberOfInstallments; i++) {
          amounts.push(baseAmount);
        }
        // 处理四舍五入误差
        const sum = amounts.reduce((a, b) => a + b, 0);
        if (Math.abs(sum - totalAmount) > 0.01) {
          amounts[amounts.length - 1] += totalAmount - sum;
        }
        break;

      default:
        // 默认使用免息分期
        const defaultAmount = Math.round((totalAmount / numberOfInstallments) * 100) / 100;
        for (let i = 0; i < numberOfInstallments; i++) {
          amounts.push(defaultAmount);
        }
        const defaultSum = amounts.reduce((a, b) => a + b, 0);
        if (Math.abs(defaultSum - totalAmount) > 0.01) {
          amounts[amounts.length - 1] += totalAmount - defaultSum;
        }
        break;
    }

    // 如果有手续费，加到第一期
    if (handlingFee > 0 && amounts.length > 0) {
      amounts[0] += handlingFee;
      amounts[0] = Math.round(amounts[0] * 100) / 100;
    }

    return amounts;
  }

  // 计算每期的还款日期
  calculateDueDate(startDate, installmentNumber) {
    return dayjs(startDate)
      .add(installmentNumber - 1, 'month')
      .format('YYYY-MM-DD');
  }

  // 获取所有分期账单
  async getInstallmentPlans() {
    if (!this.database) {
      await this.initDatabase();
    }

    const [result] = await this.database!.executeSql(`
      SELECT 
        p.*, 
        SUM(CASE WHEN d.is_paid = 1 THEN d.amount ELSE 0 END) as paid_amount,
        COUNT(CASE WHEN d.is_paid = 1 THEN 1 END) as paid_installments
      FROM installment_plans p
      LEFT JOIN installment_details d ON p.id = d.plan_id
      GROUP BY p.id
      ORDER BY p.created_at DESC
    `);

    const plans: any[] = [];
    for (let i = 0; i < result.rows.length; i++) {
      plans.push(result.rows.item(i));
    }

    return plans;
  }

  // 获取分期账单详情
  async getInstallmentPlanDetails(planId) {
    if (!this.database) {
      await this.initDatabase();
    }

    // 获取计划基本信息
    const [planResult] = await this.database!.executeSql(
      'SELECT * FROM installment_plans WHERE id = ?',
      [planId],
    );

    if (planResult.rows.length === 0) {
      return null;
    }

    const plan = planResult.rows.item(0);

    // 获取每期详情
    const [detailsResult] = await this.database!.executeSql(
      'SELECT * FROM installment_details WHERE plan_id = ? ORDER BY installment_number',
      [planId],
    );

    const details = [];
    for (let i = 0; i < detailsResult.rows.length; i++) {
      details.push(detailsResult.rows.item(i));
    }

    return {...plan, installments: details};
  }

  // 更新分期账单
  async updateInstallmentPlan(plan) {
    if (!this.database) {
      await this.initDatabase();
    }

    const {
      id,
      name,
      type,
      startDate,
      totalAmount,
      downPayment,
      numberOfInstallments,
      installmentMode,
      annualRate = 0,
      handlingFee = 0,
      autoRecord,
    } = plan;

    // 更新主表
    await this.database!.executeSql(
      `UPDATE installment_plans SET
        name = ?,
        type = ?,
        start_date = ?,
        total_amount = ?,
        down_payment = ?,
        number_of_installments = ?,
        installment_mode = ?,
        annual_rate = ?,
        handling_fee = ?,
        auto_record = ?,
        updated_at = ?
      WHERE id = ?`,
      [
        name,
        type,
        startDate,
        totalAmount,
        downPayment || 0,
        numberOfInstallments,
        installmentMode,
        annualRate,
        handlingFee,
        autoRecord,
        dayjs().format('YYYY-MM-DD HH:mm:ss'),
        id,
      ],
    );

    // 删除原有分期详情
    await this.database!.executeSql(
      'DELETE FROM installment_details WHERE plan_id = ?',
      [id],
    );

    // 重新计算并插入每期详情
    const amountAfterDownPayment = totalAmount - (downPayment || 0);
    const installmentAmount = this.calculateInstallmentAmount(
      amountAfterDownPayment,
      numberOfInstallments,
      installmentMode,
      annualRate,
      handlingFee,
    );

    for (let i = 1; i <= numberOfInstallments; i++) {
      const dueDate = this.calculateDueDate(startDate, i);
      await this.database!.executeSql(
        `INSERT INTO installment_details (
          plan_id, installment_number, due_date, amount, is_paid
        ) VALUES (?, ?, ?, ?, 0)`,
        [id, i, dueDate, installmentAmount[i - 1]],
      );
    }
  }

  // 更新分期还款状态
  async updateInstallmentPaymentStatus(
    detailId: number,
    isPaid: boolean,
    paymentDate: string | null = null,
    transactionId: number | null = null,
  ) {
    if (!this.database) {
      await this.initDatabase();
    }

    await this.database!.executeSql(
      `UPDATE installment_details SET 
        is_paid = ?, 
        payment_date = ?,
        transaction_id = ?
      WHERE id = ?`,
      [
        isPaid ? 1 : 0,
        isPaid ? paymentDate || dayjs().format('YYYY-MM-DD HH:mm:ss') : null,
        transactionId,
        detailId,
      ],
    );
  }

  // 删除分期账单
  async deleteInstallmentPlan(planId) {
    if (!this.database) {
      await this.initDatabase();
    }

    // 删除分期详情
    await this.database!.executeSql(
      'DELETE FROM installment_details WHERE plan_id = ?',
      [planId],
    );

    // 删除分期计划
    await this.database!.executeSql(
      'DELETE FROM installment_plans WHERE id = ?',
      [planId],
    );
  }

  // 在 DatabaseService 类中添加 createInstallmentTables 方法
  /**
   * 创建分期账单相关的表
   */
  async createInstallmentTables(): Promise<void> {
    if (!this.database) {
      await this.initDatabase();
    }

    try {
      // 分期账单主表
      await this.database!.executeSql(`
        CREATE TABLE IF NOT EXISTS installment_plans (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          name TEXT NOT NULL,
          type TEXT NOT NULL, 
          start_date TEXT NOT NULL,
          total_amount REAL NOT NULL,
          down_payment REAL DEFAULT 0,
          number_of_installments INTEGER NOT NULL,
          installment_mode TEXT NOT NULL, 
          auto_record INTEGER DEFAULT 1, 
          created_at TEXT NOT NULL,
          updated_at TEXT
        );
      `);

      // 分期详情表 - 存储每期的具体信息
      await this.database!.executeSql(`
        CREATE TABLE IF NOT EXISTS installment_details (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          plan_id INTEGER NOT NULL,
          installment_number INTEGER NOT NULL, 
          due_date TEXT NOT NULL,
          amount REAL NOT NULL,
          is_paid INTEGER DEFAULT 0,
          payment_date TEXT,
          transaction_id INTEGER, 
          FOREIGN KEY (plan_id) REFERENCES installment_plans (id) ON DELETE CASCADE
        );
      `);

      // 为现有的分期账单表添加新字段（如果不存在）
      try {
        await this.database!.executeSql(`
          ALTER TABLE installment_plans ADD COLUMN annual_rate REAL DEFAULT 0;
        `);
      } catch (error) {
        // 字段可能已存在，忽略错误
      }

      try {
        await this.database!.executeSql(`
          ALTER TABLE installment_plans ADD COLUMN handling_fee REAL DEFAULT 0;
        `);
      } catch (error) {
        // 字段可能已存在，忽略错误
      }

      console.log('分期账单表创建成功');
    } catch (error) {
      console.error('创建分期账单表失败:', error);
      throw error;
    }
  }

  /**
   * 获取分类信息
   */
  async getCategoryById(categoryId: string): Promise<Category | null> {
    if (!this.database) {
      await this.initDatabase();
    }

    try {
      const [result] = await this.database!.executeSql(
        'SELECT * FROM categories WHERE id = ?',
        [categoryId],
      );

      if (result.rows.length > 0) {
        return result.rows.item(0);
      }
      return null;
    } catch (error) {
      console.error('获取分类信息失败:', error);
      return null;
    }
  }

  /**
   * 执行SQL语句的辅助方法
   */
  async executeSql(query: string, params: any[] = []): Promise<any> {
    if (!this.database) {
      await this.initDatabase();
    }

    try {
      return await this.database!.executeSql(query, params);
    } catch (error) {
      console.error('执行SQL失败:', error, query, params);
      throw error;
    }
  }

  /**
   * 处理交易记录结果的辅助方法
   */
  processTransactionResults(result: any): any[] {
    const transactions = [];
    for (let i = 0; i < result[0].rows.length; i++) {
      transactions.push(result[0].rows.item(i));
    }
    return transactions;
  }

  // 创建家庭相关表
  async createFamilyTables() {
    try {
      await this.database!.transaction(tx => {
        // 家庭表
        tx.executeSql(
          `CREATE TABLE IF NOT EXISTS families (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name TEXT,
            code TEXT NOT NULL,
            created_at TEXT NOT NULL
          )`,
        );

        // 家庭成员表
        tx.executeSql(
          `CREATE TABLE IF NOT EXISTS family_members (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            family_id INTEGER NOT NULL,
            name TEXT NOT NULL,
            device_id TEXT NOT NULL,
            avatar TEXT,
            role TEXT NOT NULL,
            last_sync_time TEXT,
            FOREIGN KEY (family_id) REFERENCES families (id)
          )`,
        );

        // 在交易表中添加family_id字段 - 先检查列是否存在
        tx.executeSql('PRAGMA table_info(transactions)', [], (_, result) => {
          // 检查返回的列信息，查找是否已有family_id列
          let hasColumn = false;
          for (let i = 0; i < result.rows.length; i++) {
            if (result.rows.item(i).name === 'familyId') {
              hasColumn = true;
              break;
            }
          }

          // 如果不存在该列，则添加
          if (!hasColumn) {
            tx.executeSql(
              'ALTER TABLE transactions ADD COLUMN familyId INTEGER DEFAULT NULL',
            );
          }
        });
      });
      console.log('家庭相关表创建成功');
    } catch (error) {
      console.error('创建家庭相关表失败', error);
      throw error;
    }
  }

  // 获取家庭信息
  async getFamilyInfo(): Promise<Family | null> {
    try {
      const [result] = await this.database!.executeSql(
        'SELECT * FROM families LIMIT 1',
      );

      if (result.rows.length > 0) {
        return result.rows.item(0);
      }
      return null;
    } catch (error) {
      console.error('获取家庭信息失败', error);
      throw error;
    }
  }

  // 创建家庭并添加当前用户为管理员
  async createFamily(
    code: string,
    familyName: string,
    memberName: string,
  ): Promise<number> {
    try {
      const deviceId = await this.getDeviceId();
      const now = dayjs().format('YYYY-MM-DD HH:mm:ss');

      let familyId = 0;

      await this.database!.transaction(tx => {
        // 创建家庭
        tx.executeSql(
          'INSERT INTO families (name, code, created_at) VALUES (?, ?, ?)',
          [familyName, code, now],
          (_, result) => {
            familyId = result.insertId;

            // 添加当前用户为管理员
            tx.executeSql(
              `INSERT INTO family_members (family_id, name, device_id, role, last_sync_time) 
               VALUES (?, ?, ?, ?, ?)`,
              [familyId, memberName, deviceId, 'admin', now],
            );

            // 保存用户名到设置
            tx.executeSql(
              'INSERT OR REPLACE INTO settings (key, value) VALUES (?, ?)',
              ['user_name', memberName],
            );
          },
        );
      });

      return familyId;
    } catch (error) {
      console.error('创建家庭失败', error);
      throw error;
    }
  }

  // 通过邀请码加入家庭
  async joinFamily(code: string, memberName: string): Promise<void> {
    try {
      const deviceId = await this.getDeviceId();
      const now = dayjs().format('YYYY-MM-DD HH:mm:ss');

      // 查找邀请码对应的家庭
      const [result] = await this.database!.executeSql(
        'SELECT id FROM families WHERE code = ?',
        [code],
      );

      if (result.rows.length === 0) {
        throw new Error('无效的邀请码');
      }

      const familyId = result.rows.item(0).id;

      await this.database!.transaction(tx => {
        // 添加当前用户为普通成员
        tx.executeSql(
          `INSERT INTO family_members (family_id, name, device_id, role, last_sync_time) 
           VALUES (?, ?, ?, ?, ?)`,
          [familyId, memberName, deviceId, 'member', now],
        );

        // 保存用户名到设置
        tx.executeSql(
          'INSERT OR REPLACE INTO settings (key, value) VALUES (?, ?)',
          ['user_name', memberName],
        );
      });
    } catch (error) {
      console.error('加入家庭失败', error);
      throw error;
    }
  }

  // 获取家庭成员列表
  async getFamilyMembers(): Promise<FamilyMember[]> {
    try {
      const family = await this.getFamilyInfo();
      if (!family) {
        return [];
      }

      const [result] = await this.database!.executeSql(
        'SELECT * FROM family_members WHERE family_id = ?',
        [family.id],
      );

      const members: FamilyMember[] = [];
      for (let i = 0; i < result.rows.length; i++) {
        const item = result.rows.item(i);
        members.push({
          id: item.id,
          familyId: item.family_id,
          name: item.name,
          deviceId: item.device_id,
          avatar: item.avatar,
          role: item.role,
          lastSyncTime: item.last_sync_time,
          isOnline: false, // 默认离线，需要通过网络检测更新
        });
      }

      return members;
    } catch (error) {
      console.error('获取家庭成员失败', error);
      throw error;
    }
  }

  // 获取设备ID (实际实现)
  async getDeviceId(): Promise<string> {
    try {
      // 先从数据库中获取已保存的设备ID
      const [deviceIdResult] = await this.database!.executeSql(
        'SELECT value FROM settings WHERE key = "device_id"',
      );

      // 如果已有设备ID，直接返回
      if (deviceIdResult.rows.length > 0) {
        return deviceIdResult.rows.item(0).value;
      }

      // 没有则生成一个新的设备ID
      const newDeviceId =
        'device_' +
        Math.random().toString(36).substring(2, 10) +
        Date.now().toString(36);

      // 保存到数据库
      await this.database!.executeSql(
        'INSERT OR REPLACE INTO settings (key, value) VALUES (?, ?)',
        ['device_id', newDeviceId],
      );

      return newDeviceId;
    } catch (error) {
      // 如果出现错误，返回一个临时ID
      console.error('获取设备ID失败', error);
      return 'temp_' + Math.random().toString(36).substring(2, 10);
    }
  }

  // 获取用户名 (模拟实现)
  async getUserName(): Promise<string | null> {
    // 在实际应用中，应从用户配置或账号系统获取
    return '用户' + Math.floor(Math.random() * 1000);
  }

  // 在 DatabaseService 类中添加退出家庭方法
  async leaveFamily(): Promise<void> {
    try {
      // 获取设备ID以识别当前用户
      const deviceId = await this.getDeviceId();

      // 获取当前用户所在的家庭
      const family = await this.getFamilyInfo();
      if (!family) {
        throw new Error('您当前没有加入任何家庭');
      }

      await this.database!.transaction(tx => {
        // 从家庭成员表中删除当前用户
        tx.executeSql('DELETE FROM family_members WHERE device_id = ?', [
          deviceId,
        ]);

        // 检查家庭是否还有其他成员
        tx.executeSql(
          'SELECT COUNT(*) as count FROM family_members WHERE family_id = ?',
          [family.id],
          (_, result) => {
            const count = result.rows.item(0).count;

            // 如果没有其他成员，删除整个家庭
            if (count === 0) {
              tx.executeSql('DELETE FROM families WHERE id = ?', [family.id]);
            }
          },
        );
      });
    } catch (error) {
      console.error('退出家庭失败', error);
      throw error;
    }
  }

  // 可以添加以下方法来处理多个家庭功能 (供未来扩展使用)
  async getAllFamilies(): Promise<Family[]> {
    try {
      const deviceId = await this.getDeviceId();

      const [result] = await this.database!.executeSql(
        `SELECT f.* FROM families f
         JOIN family_members m ON f.id = m.family_id
         WHERE m.device_id = ?`,
        [deviceId],
      );

      const families: Family[] = [];
      for (let i = 0; i < result.rows.length; i++) {
        families.push(result.rows.item(i));
      }

      return families;
    } catch (error) {
      console.error('获取所有家庭失败', error);
      throw error;
    }
  }

  // 踢出家庭成员
  async removeFamilyMember(memberId: number): Promise<void> {
    try {
      // 检查当前用户是否为管理员
      const deviceId = await this.getDeviceId();
      const [adminResult] = await this.database!.executeSql(
        `SELECT * FROM family_members 
         WHERE device_id = ? AND role = 'admin'`,
        [deviceId],
      );

      if (adminResult.rows.length === 0) {
        throw new Error('只有管理员可以踢出成员');
      }

      // 踢出指定成员
      await this.database!.executeSql(
        'DELETE FROM family_members WHERE id = ?',
        [memberId],
      );
    } catch (error) {
      console.error('踢出家庭成员失败', error);
      throw error;
    }
  }

  // 添加更新家庭名称的方法
  async updateFamilyName(familyId: number, name: string): Promise<void> {
    try {
      // 检查是否为管理员
      const deviceId = await this.getDeviceId();
      const [adminResult] = await this.database!.executeSql(
        `SELECT * FROM family_members 
         WHERE family_id = ? AND device_id = ? AND role = 'admin'`,
        [familyId, deviceId],
      );

      if (adminResult.rows.length === 0) {
        throw new Error('只有管理员可以修改家庭名称');
      }

      // 开始事务，更新家庭名称和关联交易记录
      await this.database!.transaction(async tx => {
        // 1. 更新家庭表中的名称
        await tx.executeSql('UPDATE families SET name = ? WHERE id = ?', [
          name,
          familyId,
        ]);

        // 2. 更新所有关联此家庭ID的交易记录中的家庭名称
        await tx.executeSql(
          'UPDATE transactions SET family_name = ? WHERE family_id = ?',
          [name, familyId],
        );
      });

      // 发送一个通知，通知所有页面刷新家庭数据
      // 这里可以使用一个事件发射器或全局状态管理
      if (this.eventEmitter) {
        this.eventEmitter.emit('familyNameUpdated', {familyId, name});
      }

      console.log(`家庭名称已更新为: ${name}，同时更新了关联的交易记录`);
    } catch (error) {
      console.error('更新家庭名称失败', error);
      throw error;
    }
  }

  // 添加到 DatabaseService 类中

  // 获取指定ID的交易记录
  async getTransactionById(id: number): Promise<Transaction | null> {
    try {
      const [result] = await this.database!.executeSql(
        'SELECT * FROM transactions WHERE id = ?',
        [id],
      );

      if (result.rows.length === 0) {
        return null;
      }

      return result.rows.item(0);
    } catch (error) {
      console.error('获取交易记录失败', error);
      return null;
    }
  }

  // 添加来自其他设备的交易记录
  async addForeignTransaction(transaction: Transaction): Promise<string> {
    try {
      const now = new Date().toISOString();

      const [result] = await this.database!.executeSql(
        `INSERT INTO transactions (
          amount, note, date, category_id, category_name, type,
          family_id, family_name, created_at, updated_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
        [
          transaction.amount,
          transaction.note || '',
          transaction.date,
          transaction.categoryId,
          transaction.categoryName,
          transaction.type,
          transaction.familyId,
          transaction.familyName || '',
          now,
          now,
        ],
      );

      return result.insertId.toString();
    } catch (error) {
      console.error('添加外部交易记录失败', error);
      throw error;
    }
  }

  // 获取自上次同步以来的交易记录
  async getTransactionsSince(
    lastSyncTime: string,
    familyId?: number,
  ): Promise<Transaction[]> {
    try {
      let query = `
        SELECT * FROM transactions 
        WHERE updated_at > ?
      `;

      const params: any[] = [lastSyncTime];

      // 如果指定了家庭ID，只获取该家庭的交易
      if (familyId) {
        query += ' AND family_id = ?';
        params.push(familyId);
      }

      query += ' ORDER BY date DESC';

      const [result] = await this.database!.executeSql(query, params);

      return this.mapTransactionsFromResults(result);
    } catch (error) {
      console.error('获取交易记录失败', error);
      throw error;
    }
  }

  // 更新家庭成员的最后同步时间
  async updateFamilyMemberSyncTime(
    deviceId: string,
    syncTime: string,
  ): Promise<void> {
    try {
      const family = await this.getFamilyInfo();
      if (!family) {
        throw new Error('用户未加入家庭');
      }

      if (!this.database) {
        throw new Error('数据库未初始化');
      }

      await this.database.executeSql(
        `UPDATE family_members 
         SET last_sync_time = ?
         WHERE family_id = ? AND device_id = ?`,
        [syncTime, family.id, deviceId],
      );
    } catch (error) {
      console.error('更新家庭成员同步时间失败', error);
      throw error;
    }
  }

  // 根据设备ID获取家庭成员
  async getFamilyMemberByDeviceId(
    deviceId: string,
  ): Promise<FamilyMember | null> {
    try {
      const family = await this.getFamilyInfo();
      if (!family) {
        return null;
      }

      const [result] = await this.database!.executeSql(
        `SELECT * FROM family_members 
         WHERE family_id = ? AND device_id = ?`,
        [family.id, deviceId],
      );

      if (result.rows.length === 0) {
        return null;
      }

      return result.rows.item(0);
    } catch (error) {
      console.error('获取家庭成员失败', error);
      return null;
    }
  }

  // 设置家庭成员在线状态
  async setFamilyMemberOnlineStatus(
    deviceId: string,
    isOnline: boolean,
  ): Promise<void> {
    try {
      const family = await this.getFamilyInfo();
      if (!family) {
        return;
      }

      await this.database!.executeSql(
        `UPDATE family_members 
         SET is_online = ?
         WHERE family_id = ? AND device_id = ?`,
        [isOnline ? 1 : 0, family.id, deviceId],
      );
    } catch (error) {
      console.error('设置家庭成员在线状态失败', error);
    }
  }

  // 添加这个方法，用来更新数据库表结构
  async updateDatabaseSchema(): Promise<void> {
    try {
      if (!this.database) {
        throw new Error('数据库未初始化');
      }

      // 检查 family_members 表是否有 is_online 列
      const [result] = await this.database.executeSql(
        'PRAGMA table_info(family_members)',
      );

      // 检查结果中是否包含 is_online 列
      let hasIsOnlineColumn = false;
      for (let i = 0; i < result.rows.length; i++) {
        const column = result.rows.item(i);
        if (column.name === 'is_online') {
          hasIsOnlineColumn = true;
          break;
        }
      }

      // 如果没有 is_online 列，添加它
      if (!hasIsOnlineColumn) {
        await this.database.executeSql(
          'ALTER TABLE family_members ADD COLUMN is_online INTEGER DEFAULT 0',
        );
        console.log('已添加 is_online 列到 family_members 表');
      }
    } catch (error) {
      console.error('更新数据库结构失败', error);
    }
  }

  // 添加一个新方法专门用于更新成员在线状态
  async updateFamilyMemberOnlineStatus(
    deviceId: string,
    isOnline: boolean,
  ): Promise<void> {
    try {
      if (!this.database) {
        throw new Error('数据库未初始化');
      }

      const family = await this.getFamilyInfo();
      if (!family) {
        return; // 如果没有家庭，无需更新
      }

      await this.database.executeSql(
        `UPDATE family_members 
         SET is_online = ?
         WHERE family_id = ? AND device_id = ?`,
        [isOnline ? 1 : 0, family.id, deviceId],
      );
    } catch (error) {
      console.error('更新家庭成员在线状态失败', error);
      // 不抛出异常，因为这不是关键功能
    }
  }

  // 添加这个方法，用于获取特定家庭的所有交易记录
  async getTransactionsByFamilyId(familyId: number): Promise<Transaction[]> {
    try {
      if (!this.database) {
        throw new Error('数据库未初始化');
      }

      // 首先检查transactions表是否有family_id列
      const [columnsResult] = await this.database.executeSql(
        'PRAGMA table_info(transactions)',
      );

      let hasColumn = false;
      let columnName = '';

      // 检查是否有family_id或familyId列
      for (let i = 0; i < columnsResult.rows.length; i++) {
        const column = columnsResult.rows.item(i);
        if (column.name === 'family_id' || column.name === 'familyId') {
          hasColumn = true;
          columnName = column.name;
          break;
        }
      }

      if (!hasColumn) {
        console.log('transactions表中没有family_id或familyId列，返回空结果');
        return [];
      }

      // 使用正确的列名执行查询
      const [result] = await this.database.executeSql(
        `SELECT * FROM transactions WHERE ${columnName} = ? ORDER BY date DESC`,
        [familyId],
      );

      return this.mapTransactionsFromResults(result);
    } catch (error) {
      console.error('按家庭ID获取交易记录失败', error);
      return [];
    }
  }

  // 添加一个方法来确保transactions表有所需的列
  async ensureTransactionsTableHasFamilyColumn(): Promise<void> {
    try {
      if (!this.database) {
        throw new Error('数据库未初始化');
      }

      // 检查transactions表是否有family_id列
      const [columnsResult] = await this.database.executeSql(
        'PRAGMA table_info(transactions)',
      );

      let hasFamilyIdColumn = false;
      let hasFamilyNameColumn = false;

      for (let i = 0; i < columnsResult.rows.length; i++) {
        const column = columnsResult.rows.item(i);
        if (column.name === 'familyId') {
          hasFamilyIdColumn = true;
        } else if (column.name === 'familyName') {
          hasFamilyNameColumn = true;
        }
      }

      // 如果缺少必要的列，添加它们
      if (!hasFamilyIdColumn) {
        await this.database.executeSql(
          'ALTER TABLE transactions ADD COLUMN familyId INTEGER',
        );
        console.log('已添加 familyId 列到 transactions 表');
      }

      if (!hasFamilyNameColumn) {
        await this.database.executeSql(
          'ALTER TABLE transactions ADD COLUMN familyName TEXT',
        );
        console.log('已添加 familyName 列到 transactions 表');
      }
    } catch (error) {
      console.error('检查/更新transactions表结构失败', error);
    }
  }

  // 创建商品相关表
  async createProductTables(): Promise<void> {
    try {
      // 创建商品表
      await this.database!.executeSql(`
        CREATE TABLE IF NOT EXISTS products (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          name TEXT NOT NULL,
          thumbnail TEXT,
          notes TEXT,
          created_at TEXT NOT NULL,
          updated_at TEXT NOT NULL,
          purchased INTEGER DEFAULT 0
        )
      `);

      // 创建商品价格表，添加platform字段
      await this.database!.executeSql(`
        CREATE TABLE IF NOT EXISTS product_prices (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          product_id INTEGER NOT NULL,
          price REAL NOT NULL,
          platform TEXT,
          is_purchase_price INTEGER DEFAULT 0,
          created_at TEXT NOT NULL,
          updated_at TEXT NOT NULL,
          FOREIGN KEY (product_id) REFERENCES products (id) ON DELETE CASCADE
        )
      `);

      console.log('商品相关表创建成功');
    } catch (error) {
      console.error('创建商品相关表失败:', error);
      throw error;
    }
  }

  // 添加检查并升级表结构的方法，确保platform字段存在
  async ensureProductTablesHaveRequiredColumns(): Promise<void> {
    try {
      // 检查product_prices表是否有platform字段
      const [columnsResult] = await this.database!.executeSql(
        'PRAGMA table_info(product_prices)',
      );

      let hasPlatformColumn = false;
      for (let i = 0; i < columnsResult.rows.length; i++) {
        const column = columnsResult.rows.item(i);
        if (column.name === 'platform') {
          hasPlatformColumn = true;
          break;
        }
      }

      if (!hasPlatformColumn) {
        await this.database!.executeSql(
          'ALTER TABLE product_prices ADD COLUMN platform TEXT',
        );
        console.log('已添加 platform 列到 product_prices 表');
      }
    } catch (error) {
      console.error('更新商品价格表结构失败:', error);
    }
  }

  // 添加或更新商品
  async saveProduct(product: Product): Promise<number> {
    try {
      const now = dayjs().format('YYYY-MM-DD HH:mm:ss');

      if (product.id) {
        // 更新现有商品
        await this.database.executeSql(
          `UPDATE products 
           SET name = ?, thumbnail = ?, notes = ?, updated_at = ?, purchased = ?
           WHERE id = ?`,
          [
            product.name,
            product.thumbnail || null,
            product.notes || null,
            now,
            product.purchased || 0,
            product.id,
          ],
        );
        return product.id;
      } else {
        // 添加新商品
        const [result] = await this.database.executeSql(
          `INSERT INTO products (name, thumbnail, notes, created_at, updated_at, purchased)
           VALUES (?, ?, ?, ?, ?, ?)`,
          [
            product.name,
            product.thumbnail || null,
            product.notes || null,
            now,
            now,
            product.purchased || 0,
          ],
        );
        return result.insertId;
      }
    } catch (error) {
      console.error('保存商品失败:', error);
      throw error;
    }
  }

  // 添加商品价格
  async addProductPrice(productPrice: ProductPrice): Promise<number> {
    try {
      const now = dayjs().format('YYYY-MM-DD HH:mm:ss');

      const [result] = await this.database.executeSql(
        `INSERT INTO product_prices (product_id, price, platform, created_at, updated_at)
         VALUES (?, ?, ?, ?, ?)`,
        [
          productPrice.productId,
          productPrice.price,
          productPrice.platform || '',
          productPrice.createdAt || now,
          productPrice.updatedAt || now,
        ],
      );
      return result.insertId;
    } catch (error) {
      console.error('添加商品价格失败:', error);
      throw error;
    }
  }

  // 更新商品价格
  async updateProductPrice(productPrice: ProductPrice): Promise<void> {
    try {
      const now = dayjs().format('YYYY-MM-DD HH:mm:ss');

      await this.database.executeSql(
        `UPDATE product_prices
         SET price = ?, platform = ?, updated_at = ?
         WHERE id = ?`,
        [productPrice.price, productPrice.platform || '', now, productPrice.id],
      );
    } catch (error) {
      console.error('更新商品价格失败:', error);
      throw error;
    }
  }

  // 删除商品价格
  async deleteProductPrice(priceId: number): Promise<void> {
    try {
      await this.database.executeSql(
        'DELETE FROM product_prices WHERE id = ?',
        [priceId],
      );
    } catch (error) {
      console.error('删除商品价格失败:', error);
      throw error;
    }
  }

  // 获取所有商品
  async getAllProducts(): Promise<Product[]> {
    try {
      const [result] = await this.database.executeSql(
        'SELECT * FROM products ORDER BY created_at DESC',
      );

      const products: Product[] = [];
      for (let i = 0; i < result.rows.length; i++) {
        products.push(result.rows.item(i));
      }

      return products;
    } catch (error) {
      console.error('获取所有商品失败:', error);
      throw error;
    }
  }

  // 通过ID获取商品
  async getProductById(productId: number): Promise<Product | null> {
    try {
      const [result] = await this.database.executeSql(
        'SELECT * FROM products WHERE id = ?',
        [productId],
      );

      if (result.rows.length > 0) {
        return result.rows.item(0);
      }

      return null;
    } catch (error) {
      console.error('获取商品失败:', error);
      throw error;
    }
  }

  // 获取商品的所有价格
  async getProductPrices(productId: number): Promise<any[]> {
    try {
      const [result] = await this.database!.executeSql(
        'SELECT * FROM product_prices WHERE product_id = ? ORDER BY created_at DESC',
        [productId],
      );

      const prices = [];
      for (let i = 0; i < result.rows.length; i++) {
        const item = result.rows.item(i);
        prices.push({
          id: item.id,
          productId: item.product_id,
          price: item.price,
          platform: item.platform,
          isPurchasePrice: item.is_purchase_price === 1,
          createdAt: item.created_at,
          updatedAt: item.updated_at,
        });
      }

      return prices;
    } catch (error) {
      console.error('获取商品价格记录失败:', error);
      throw error;
    }
  }

  // 删除商品
  async deleteProduct(productId: number): Promise<void> {
    try {
      await this.database!.executeSql('DELETE FROM products WHERE id = ?', [
        productId,
      ]);
      await this.database!.executeSql(
        'DELETE FROM product_prices WHERE product_id = ?',
        [productId],
      );
    } catch (error) {
      console.error('删除商品失败:', error);
      throw error;
    }
  }

  // 更新商品的购买状态
  async updateProductPurchased(
    productId: number,
    purchased: boolean,
    priceId: number,
  ): Promise<void> {
    try {
      const now = dayjs().format('YYYY-MM-DD HH:mm:ss');

      await this.database!.executeSql(
        `UPDATE products 
         SET purchased = ?, updated_at = ?
         WHERE id = ?`,
        [purchased ? 1 : 0, now, productId],
      );
      // 更新商品价格表
      await this.database!.executeSql(
        `UPDATE product_prices
         SET is_purchase_price = ?, updated_at = ?
         WHERE id = ?`,
        [purchased ? 1 : 0, now, priceId],
      );
    } catch (error) {
      console.error('更新商品购买状态失败:', error);
      throw error;
    }
  }

  // 从商品创建交易记录
  async createTransactionFromProduct(
    productId: number,
    productName: string,
    price: number,
    priceId: number,
  ): Promise<void> {
    try {
      // 获取购物类别信息
      const [categoryResult] = await this.database!.executeSql(
        "SELECT id, name FROM categories WHERE name LIKE '%购物%' LIMIT 1",
      );

      let categoryId, categoryName;

      if (categoryResult.rows.length > 0) {
        // 找到购物类别
        categoryId = categoryResult.rows.item(0).id;
        categoryName = categoryResult.rows.item(0).name;
      } else {
        // 如果没找到购物类别，使用默认的其他类别
        const [defaultResult] = await this.database!.executeSql(
          "SELECT id, name FROM categories WHERE name = '其他' LIMIT 1",
        );

        if (defaultResult.rows.length > 0) {
          categoryId = defaultResult.rows.item(0).id;
          categoryName = defaultResult.rows.item(0).name;
        } else {
          throw new Error('无法找到适合的交易类别');
        }
      }

      // 使用当前日期时间
      const date = dayjs().format('YYYY-MM-DD');

      // 调用已有的addTransaction方法
      await this.addTransaction({
        amount: price,
        note: productName,
        date,
        categoryId,
        categoryName,
        type: 'expense',
        familyId: null,
        familyName: null,
        isReimbursable: false,
      });

      // 更新产品为已购买状态
      await this.updateProductPurchased(productId, true, priceId);
    } catch (error) {
      console.error('从商品创建交易失败:', error);
      throw error;
    }
  }

  // 创建借款表
  private async createLoanTable(db) {
    await db.executeSql(`
      CREATE TABLE IF NOT EXISTS loan_records (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        type TEXT NOT NULL,
        amount REAL NOT NULL,
        note TEXT,
        loanDate TEXT NOT NULL
      );
    `);
    await db.executeSql(`
      CREATE TABLE IF NOT EXISTS loan_repayments (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        loanId INTEGER NOT NULL,
        amount REAL NOT NULL,
        date TEXT NOT NULL,
        FOREIGN KEY (loanId) REFERENCES loan_records(id) ON DELETE CASCADE
      );
    `);
  }

  // 新增：添加借款记录
  async addLoanRecord(record: LoanRecord) {
    const db = await this.database;
    const res = await db.executeSql(
      'INSERT INTO loan_records (type, amount, note, loanDate) VALUES (?, ?, ?, ?)',
      [record.type, record.amount, record.note || '', record.loanDate],
    );
    const loanId = res[0].insertId;
    // 插入还款记录
    if (record.repayments && record.repayments.length > 0) {
      for (const repay of record.repayments) {
        await db.executeSql(
          'INSERT INTO loan_repayments (loanId, amount, date) VALUES (?, ?, ?)',
          [loanId, repay.amount, repay.date],
        );
      }
    }
    return loanId;
  }

  // 新增：更新借款记录
  async updateLoanRecord(record: LoanRecord) {
    const db = await this.database;
    await db.executeSql(
      'UPDATE loan_records SET type=?, amount=?, note=?, loanDate=? WHERE id=?',
      [
        record.type,
        record.amount,
        record.note || '',
        record.loanDate,
        record.id,
      ],
    );
    // 删除原有还款记录
    await db.executeSql('DELETE FROM loan_repayments WHERE loanId=?', [
      record.id,
    ]);
    // 重新插入还款记录
    if (record.repayments && record.repayments.length > 0) {
      for (const repay of record.repayments) {
        await db.executeSql(
          'INSERT INTO loan_repayments (loanId, amount, date) VALUES (?, ?, ?)',
          [record.id, repay.amount, repay.date],
        );
      }
    }
  }

  // 新增：删除借款记录
  async deleteLoanRecord(id: number) {
    const db = await this.database;
    await db.executeSql('DELETE FROM loan_records WHERE id=?', [id]);
    // 还款记录会自动级联删除
  }

  // 新增：获取所有借款记录（含还款记录）
  async getAllLoanRecords(): Promise<LoanRecord[]> {
    const db = await this.database;
    const res = await db.executeSql(
      'SELECT * FROM loan_records ORDER BY loanDate DESC',
    );
    const records: LoanRecord[] = [];
    for (const row of res[0].rows.raw()) {
      const repaymentsRes = await db.executeSql(
        'SELECT amount, date FROM loan_repayments WHERE loanId=? ORDER BY date ASC',
        [row.id],
      );
      const repayments = repaymentsRes[0].rows.raw();
      records.push({
        ...row,
        repayments,
      });
    }
    return records;
  }

  // 新增：通过id获取单条借款记录（含还款记录）
  async getLoanRecordById(id: number): Promise<LoanRecord | null> {
    const db = await this.database;
    const res = await db.executeSql('SELECT * FROM loan_records WHERE id=?', [
      id,
    ]);
    if (res[0].rows.length === 0) {
      return null;
    }
    const row = res[0].rows.item(0);
    const repaymentsRes = await db.executeSql(
      'SELECT amount, date FROM loan_repayments WHERE loanId=? ORDER BY date ASC',
      [row.id],
    );
    const repayments = repaymentsRes[0].rows.raw();
    return {
      ...row,
      repayments,
    };
  }

  // ==================== 账本管理方法 ====================

  // 初始化默认账本
  async initDefaultAccountBook(): Promise<void> {
    try {
      if (!this.database) {
        await this.initDatabase();
      }

      // 检查是否已存在默认账本
      const [result] = await this.database!.executeSql(
        'SELECT * FROM account_books WHERE isDefault = 1',
      );

      if (result.rows.length === 0) {
        // 创建默认账本
        const now = dayjs().format('YYYY-MM-DD HH:mm:ss');
        await this.database!.executeSql(
          'INSERT INTO account_books (name, description, isDefault, createdAt) VALUES (?, ?, ?, ?)',
          ['默认账本', '系统默认账本', 1, now],
        );
        console.log('默认账本创建成功');
      }
    } catch (error) {
      console.error('初始化默认账本失败:', error);
      throw error;
    }
  }

  // 获取所有账本
  async getAllAccountBooks(): Promise<AccountBook[]> {
    try {
      if (!this.database) {
        await this.initDatabase();
      }

      const [result] = await this.database!.executeSql(
        'SELECT * FROM account_books ORDER BY isDefault DESC, createdAt ASC',
      );

      const accountBooks: AccountBook[] = [];
      for (let i = 0; i < result.rows.length; i++) {
        const row = result.rows.item(i);
        accountBooks.push({
          id: row.id,
          name: row.name,
          description: row.description,
          isDefault: row.isDefault === 1,
          createdAt: row.createdAt,
          updatedAt: row.updatedAt,
        });
      }

      return accountBooks;
    } catch (error) {
      console.error('获取账本列表失败:', error);
      throw error;
    }
  }

  // 添加账本
  async addAccountBook(accountBook: Omit<AccountBook, 'id' | 'createdAt'>): Promise<number> {
    try {
      if (!this.database) {
        await this.initDatabase();
      }

      const now = dayjs().format('YYYY-MM-DD HH:mm:ss');
      const [result] = await this.database!.executeSql(
        'INSERT INTO account_books (name, description, isDefault, createdAt) VALUES (?, ?, ?, ?)',
        [accountBook.name, accountBook.description || '', accountBook.isDefault ? 1 : 0, now],
      );

      return result.insertId || 0;
    } catch (error) {
      console.error('添加账本失败:', error);
      throw error;
    }
  }

  // 更新账本
  async updateAccountBook(accountBook: AccountBook): Promise<void> {
    try {
      if (!this.database) {
        await this.initDatabase();
      }

      if (!accountBook.id) {
        throw new Error('账本ID不能为空');
      }

      const now = dayjs().format('YYYY-MM-DD HH:mm:ss');
      await this.database!.executeSql(
        'UPDATE account_books SET name = ?, description = ?, isDefault = ?, updatedAt = ? WHERE id = ?',
        [accountBook.name, accountBook.description || '', accountBook.isDefault ? 1 : 0, now, accountBook.id],
      );
    } catch (error) {
      console.error('更新账本失败:', error);
      throw error;
    }
  }

  // 删除账本
  async deleteAccountBook(id: number): Promise<void> {
    try {
      if (!this.database) {
        await this.initDatabase();
      }

      // 检查是否为默认账本
      const [checkResult] = await this.database!.executeSql(
        'SELECT isDefault FROM account_books WHERE id = ?',
        [id],
      );

      if (checkResult.rows.length > 0 && checkResult.rows.item(0).isDefault === 1) {
        throw new Error('不能删除默认账本');
      }

      // 检查是否有关联的交易记录
      const [transactionResult] = await this.database!.executeSql(
        'SELECT COUNT(*) as count FROM transactions WHERE accountBookId = ?',
        [id],
      );

      if (transactionResult.rows.item(0).count > 0) {
        throw new Error('该账本下还有交易记录，无法删除');
      }

      // 删除账本
      await this.database!.executeSql(
        'DELETE FROM account_books WHERE id = ?',
        [id],
      );
    } catch (error) {
      console.error('删除账本失败:', error);
      throw error;
    }
  }

  // 获取默认账本
  async getDefaultAccountBook(): Promise<AccountBook | null> {
    try {
      if (!this.database) {
        await this.initDatabase();
      }

      const [result] = await this.database!.executeSql(
        'SELECT * FROM account_books WHERE isDefault = 1',
      );

      if (result.rows.length > 0) {
        const row = result.rows.item(0);
        return {
          id: row.id,
          name: row.name,
          description: row.description,
          isDefault: row.isDefault === 1,
          createdAt: row.createdAt,
          updatedAt: row.updatedAt,
        };
      }

      return null;
    } catch (error) {
      console.error('获取默认账本失败:', error);
      throw error;
    }
  }

  // 根据ID获取账本
  async getAccountBookById(id: number): Promise<AccountBook | null> {
    try {
      if (!this.database) {
        await this.initDatabase();
      }

      const [result] = await this.database!.executeSql(
        'SELECT * FROM account_books WHERE id = ?',
        [id],
      );

      if (result.rows.length > 0) {
        const row = result.rows.item(0);
        return {
          id: row.id,
          name: row.name,
          description: row.description,
          isDefault: row.isDefault === 1,
          createdAt: row.createdAt,
          updatedAt: row.updatedAt,
        };
      }

      return null;
    } catch (error) {
      console.error('获取账本失败:', error);
      throw error;
    }
  }
}

// 创建单例实例
const databaseService = new DatabaseService();
export default databaseService;
